#!/usr/bin/env python3
"""
Service MNT RÉEL pour C2-EW
Utilise les vrais fichiers n[LAT]_w[LON]_1arc_v3.tif pour des calculs précis
CRITIQUE POUR MISSIONS SENSIBLES
"""

import os
import re
import math
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import numpy as np

# Configuration environnement pour éviter conflits PROJ
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

try:
    import rasterio
    from rasterio.windows import Window
    RASTERIO_AVAILABLE = True
    print("✅ Rasterio disponible - Utilisation des vrais fichiers MNT")
except ImportError:
    RASTERIO_AVAILABLE = False
    print("⚠️ Rasterio non disponible - Mode simulation")

class MNTServiceReal:
    """Service MNT utilisant les vrais fichiers TIFF"""
    
    def __init__(self, mnt_directory: str = None):
        if mnt_directory is None:
            self.mnt_directory = Path(__file__).parent / "app" / "MNT"
        else:
            self.mnt_directory = Path(mnt_directory)
        
        self.file_cache = {}  # Cache des handles de fichiers
        self.elevation_cache = {}  # Cache des élévations
        self.file_index = {}  # Index des fichiers par coordonnées
        
        print(f"📁 Dossier MNT: {self.mnt_directory}")
        
        # Construire l'index des fichiers
        self._build_file_index()
    
    def _build_file_index(self):
        """Construire l'index des fichiers MNT"""
        if not self.mnt_directory.exists():
            print(f"❌ Dossier MNT non trouvé: {self.mnt_directory}")
            return
        
        pattern = re.compile(r'n(\d+)_w(\d+)_1arc_v3\.tif')
        
        for tiff_file in self.mnt_directory.glob("*.tif"):
            match = pattern.match(tiff_file.name)
            if match:
                lat = int(match.group(1))
                lon = int(match.group(2))
                
                # Bounds du fichier (1° x 1°)
                min_lat = lat
                max_lat = lat + 1
                min_lon = -lon - 1  # w007 = -8° à -7°
                max_lon = -lon
                
                self.file_index[(lat, lon)] = {
                    'path': tiff_file,
                    'bounds': (min_lon, min_lat, max_lon, max_lat),
                    'lat_key': lat,
                    'lon_key': lon
                }
        
        print(f"📊 Index MNT: {len(self.file_index)} fichiers indexés")
    
    def _find_file_for_point(self, lon: float, lat: float) -> Optional[Dict]:
        """Trouver le fichier MNT contenant un point"""
        for (lat_key, lon_key), file_info in self.file_index.items():
            bounds = file_info['bounds']
            min_lon, min_lat, max_lon, max_lat = bounds
            
            if min_lon <= lon < max_lon and min_lat <= lat < max_lat:
                return file_info
        
        return None
    
    def get_elevation_real(self, lon: float, lat: float) -> Optional[float]:
        """Obtenir l'élévation RÉELLE depuis les fichiers TIFF"""
        if not RASTERIO_AVAILABLE:
            return self._get_elevation_simulation(lon, lat)
        
        # Vérifier le cache
        cache_key = f"{lon:.6f},{lat:.6f}"
        if cache_key in self.elevation_cache:
            return self.elevation_cache[cache_key]
        
        # Trouver le fichier approprié
        file_info = self._find_file_for_point(lon, lat)
        if not file_info:
            return None
        
        try:
            # Ouvrir le fichier (avec cache)
            file_path = file_info['path']
            if str(file_path) not in self.file_cache:
                self.file_cache[str(file_path)] = rasterio.open(file_path)
            
            dataset = self.file_cache[str(file_path)]
            
            # Convertir coordonnées en indices de pixel
            row, col = dataset.index(lon, lat)
            
            # Vérifier que le point est dans les limites
            if 0 <= row < dataset.height and 0 <= col < dataset.width:
                # Lire la valeur d'élévation
                window = Window(col, row, 1, 1)
                data = dataset.read(1, window=window)
                elevation = data[0, 0]
                
                # Vérifier les valeurs NoData
                if dataset.nodata is not None and elevation == dataset.nodata:
                    return None
                
                # Mettre en cache
                self.elevation_cache[cache_key] = float(elevation)
                return float(elevation)
            
        except Exception as e:
            print(f"⚠️ Erreur lecture MNT pour ({lon}, {lat}): {e}")
        
        return None
    
    def _get_elevation_simulation(self, lon: float, lat: float) -> Optional[float]:
        """Simulation d'élévation (fallback si rasterio indisponible)"""
        if -18.0 <= lon <= -1.0 and 20.0 <= lat <= 36.0:
            # Simulation basée sur la géographie réelle du Maroc
            atlas_lat, atlas_lon = 31.5, -7.5
            distance_atlas = math.sqrt((lat - atlas_lat)**2 + (lon - atlas_lon)**2)
            
            base_elevation = max(0, 2000 - distance_atlas * 200)
            noise = math.sin(lon * 10) * math.cos(lat * 10) * 100
            elevation = base_elevation + noise
            
            return max(0, elevation)
        return None
    
    def calculate_distance(self, lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """Calculer la distance précise entre deux points"""
        R = 6371000  # Rayon de la Terre en mètres
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def calculate_line_of_sight_real(self, observer_lon: float, observer_lat: float,
                                   target_lon: float, target_lat: float,
                                   observer_height: float = 2.0,
                                   target_height: float = 0.0) -> Dict:
        """
        Calculer la ligne de vue selon le FLUX EXACT spécifié
        1. Projection des coordonnées
        2. Recherche des tuiles via R-tree
        3. Extraction du profil d'élévation
        4. Calcul de la ligne de visée
        """
        import time
        start_time = time.time()

        # 1. Calculer la distance horizontale totale L (haversine)
        distance_m = self.calculate_distance(observer_lon, observer_lat, target_lon, target_lat)

        # 2. Obtenir les élévations des points de départ et d'arrivée
        z_obs_terrain = self.get_elevation_real(observer_lon, observer_lat)
        z_tgt_terrain = self.get_elevation_real(target_lon, target_lat)

        if z_obs_terrain is None or z_tgt_terrain is None:
            return {
                'visible': False,
                'distance_m': distance_m,
                'reason': 'Données MNT manquantes',
                'calculation_time_ms': (time.time() - start_time) * 1000
            }

        # Élévations totales (terrain + hauteur)
        z_obs = z_obs_terrain + observer_height
        z_tgt = z_tgt_terrain + target_height

        # 3. Extraction du profil d'élévation - Échantillonnage adaptatif
        if distance_m < 1000:  # < 1km
            sample_interval = 10  # 1 point tous les 10m
        elif distance_m < 5000:  # < 5km
            sample_interval = 25  # 1 point tous les 25m
        else:
            sample_interval = 50  # 1 point tous les 50m

        num_points = min(int(distance_m / sample_interval), 200)  # Max 200 points
        if num_points < 2:
            num_points = 2

        # 4. Calcul de la ligne de visée selon votre formule exacte
        print(f"🔍 Profil: z_obs={z_obs:.1f}m, z_tgt={z_tgt:.1f}m, points={num_points}")

        for i in range(1, num_points - 1):  # Exclure les points de départ et d'arrivée
            # Distance d le long du segment
            d = (i / (num_points - 1)) * distance_m
            ratio = d / distance_m

            # Position intermédiaire
            inter_lon = observer_lon + (target_lon - observer_lon) * ratio
            inter_lat = observer_lat + (target_lat - observer_lat) * ratio

            # Élévation du terrain à ce point
            z_terrain = self.get_elevation_real(inter_lon, inter_lat)

            if z_terrain is None:
                print(f"⚠️ Point {i}: Pas de données MNT à ({inter_lon:.4f}, {inter_lat:.4f})")
                continue  # Ignorer les points sans données

            # Hauteur attendue sur la droite de visée (VOTRE FORMULE EXACTE)
            hauteur_attendue = z_obs + (z_tgt - z_obs) * ratio

            print(f"📊 Point {i}: terrain={z_terrain:.1f}m, attendu={hauteur_attendue:.1f}m, ratio={ratio:.2f}")

            # Décision : si z_terrain > hauteur attendue ⇒ obstruction
            if z_terrain > hauteur_attendue:
                calculation_time = (time.time() - start_time) * 1000
                print(f"🔴 OBSTRUCTION à {d:.0f}m: terrain {z_terrain:.1f}m > attendu {hauteur_attendue:.1f}m")
                return {
                    'visible': False,
                    'distance_m': distance_m,
                    'obstruction_distance_m': d,
                    'obstruction_elevation_m': z_terrain,
                    'required_elevation_m': hauteur_attendue,
                    'calculation_time_ms': calculation_time,
                    'data_source': 'MNT_REAL'
                }

        # Si aucun point n'obstrue ⇒ visible = true
        calculation_time = (time.time() - start_time) * 1000
        print(f"🟢 VISIBLE: Aucune obstruction détectée")
        return {
            'visible': True,
            'distance_m': distance_m,
            'observer_elevation_m': z_obs,
            'target_elevation_m': z_tgt,
            'calculation_time_ms': calculation_time,
            'data_source': 'MNT_REAL'
        }
    
    def get_stats(self) -> Dict:
        """Obtenir les statistiques du service"""
        return {
            'total_files': len(self.file_index),
            'cache_size': len(self.file_cache),
            'elevation_cache_size': len(self.elevation_cache),
            'rasterio_available': RASTERIO_AVAILABLE,
            'data_source': 'MNT_REAL' if RASTERIO_AVAILABLE else 'SIMULATION',
            'mnt_directory': str(self.mnt_directory)
        }
    
    def close_all_handles(self):
        """Fermer tous les handles de fichiers"""
        for dataset in self.file_cache.values():
            try:
                dataset.close()
            except:
                pass
        self.file_cache.clear()
    
    def __del__(self):
        """Nettoyage lors de la destruction"""
        self.close_all_handles()

# Instance globale
mnt_service_real = MNTServiceReal()
