#!/usr/bin/env python3
"""
Téléchargement tuiles zoom 13-14 pour Sidi Yahia du Gharb
Région autour de Kenitra et Sidi Slimane
"""

import os
import requests
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def download_tile(tile_info):
    """Télécharge une tuile"""
    x, y, z, source_url, base_dir, source_name = tile_info
    
    tile_dir = os.path.join(base_dir, source_name, str(z), str(x))
    tile_path = os.path.join(tile_dir, f"{y}.png")
    
    # Vérifier si existe déjà
    if os.path.exists(tile_path):
        return f"Existe: {z}/{x}/{y}"
    
    url = source_url.format(x=x, y=y, z=z)
    
    try:
        os.makedirs(tile_dir, exist_ok=True)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        with open(tile_path, 'wb') as f:
            f.write(response.content)
        
        return f"✓ {z}/{x}/{y}"
        
    except Exception as e:
        return f"✗ {z}/{x}/{y}: {str(e)}"

def main():
    print("=" * 80)
    print("  TÉLÉCHARGEMENT TUILES ZOOM 13-14 - SIDI YAHIA DU GHARB")
    print("=" * 80)
    
    base_dir = os.path.join(os.path.dirname(__file__), "..", "tiles")
    source_name = "esri_satellite_morocco"
    source_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    
    # Coordonnées de Sidi Yahia du Gharb et région
    sidi_yahia = (34.3000, -6.3000)  # Sidi Yahia du Gharb
    kenitra = (34.2610, -6.5802)     # Kenitra
    sidi_slimane = (34.2667, -5.9167) # Sidi Slimane
    
    print("🎯 RÉGION SIDI YAHIA DU GHARB - ZOOM 13-14:")
    print(f"   📍 Centre: Sidi Yahia du Gharb ({sidi_yahia[0]}°N, {sidi_yahia[1]}°W)")
    print(f"   📍 Ouest: Kenitra ({kenitra[0]}°N, {kenitra[1]}°W)")
    print(f"   📍 Est: Sidi Slimane ({sidi_slimane[0]}°N, {sidi_slimane[1]}°W)")
    
    # Définir les limites avec marge
    margin = 0.05  # ~5km de marge
    bounds = {
        'north': max(sidi_yahia[0], kenitra[0], sidi_slimane[0]) + margin,
        'south': min(sidi_yahia[0], kenitra[0], sidi_slimane[0]) - margin,
        'east': max(sidi_yahia[1], kenitra[1], sidi_slimane[1]) + margin,
        'west': min(sidi_yahia[1], kenitra[1], sidi_slimane[1]) - margin
    }
    
    print(f"\n📊 LIMITES CALCULÉES:")
    print(f"   Nord:  {bounds['north']:7.4f}°N")
    print(f"   Sud:   {bounds['south']:7.4f}°N")
    print(f"   Est:   {bounds['east']:7.4f}°W")
    print(f"   Ouest: {bounds['west']:7.4f}°W")
    
    # Télécharger pour zoom 13 et 14
    all_tiles = []
    total_by_zoom = {}
    
    for zoom in [13, 14]:
        print(f"\n📈 CALCUL TUILES ZOOM {zoom}:")
        
        x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
        x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
        
        # Corriger l'ordre des Y
        if y_min > y_max:
            y_min, y_max = y_max, y_min
        
        # Ajuster les limites
        x_min = max(0, x_min)
        x_max = min(2**zoom - 1, x_max)
        y_min = max(0, y_min)
        y_max = min(2**zoom - 1, y_max)
        
        tiles_count = (x_max - x_min + 1) * (y_max - y_min + 1)
        total_by_zoom[zoom] = tiles_count
        
        print(f"   X: {x_min} → {x_max} ({x_max - x_min + 1} tuiles)")
        print(f"   Y: {y_min} → {y_max} ({y_max - y_min + 1} tuiles)")
        print(f"   Total: {tiles_count} tuiles")
        
        # Ajouter les tuiles à la liste
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                all_tiles.append((x, y, zoom, source_url, base_dir, source_name))
    
    total_tiles = len(all_tiles)
    estimated_size_mb = total_tiles * 0.025  # ~25KB par tuile
    
    print(f"\n📊 RÉSUMÉ TOTAL:")
    for zoom, count in total_by_zoom.items():
        print(f"   Zoom {zoom}: {count} tuiles")
    print(f"   Total: {total_tiles} tuiles")
    print(f"   Taille estimée: {estimated_size_mb:.1f} MB")
    
    if total_tiles > 2000:
        print(f"\n⚠️  ATTENTION: {total_tiles} tuiles à télécharger!")
        response = input("Continuer? (o/N): ")
        if response.lower() != 'o':
            print("Téléchargement annulé.")
            return
    
    print(f"\n🚀 DÉBUT DU TÉLÉCHARGEMENT...")
    print(f"   Source: {source_name}")
    print(f"   Zooms: 13-14")
    print(f"   Tuiles: {len(all_tiles)}")
    
    # Téléchargement avec barre de progression
    start_time = time.time()
    downloaded = 0
    errors = 0
    existing = 0
    
    with ThreadPoolExecutor(max_workers=6) as executor:
        with tqdm(total=len(all_tiles), desc="Téléchargement") as pbar:
            future_to_tile = {executor.submit(download_tile, tile): tile for tile in all_tiles}
            
            for future in as_completed(future_to_tile):
                result = future.result()
                pbar.update(1)
                
                if result.startswith("✓"):
                    downloaded += 1
                elif result.startswith("Existe"):
                    existing += 1
                else:
                    errors += 1
                
                # Mise à jour de la description
                pbar.set_description(f"✓{downloaded} Existe:{existing} ✗{errors}")
                
                time.sleep(0.05)  # Délai réduit pour zoom élevé
    
    elapsed_time = time.time() - start_time
    
    print(f"\n✅ TÉLÉCHARGEMENT TERMINÉ!")
    print(f"   Temps: {elapsed_time:.1f}s")
    print(f"   Téléchargées: {downloaded}")
    print(f"   Existantes: {existing}")
    print(f"   Erreurs: {errors}")
    print(f"   Total: {downloaded + existing + errors}")
    
    if downloaded > 0:
        print(f"\n📁 Tuiles sauvées dans: {os.path.join(base_dir, source_name)}")
        print(f"🌐 URL serveur: http://localhost:8000/tiles/{source_name}/{{z}}/{{x}}/{{y}}.png")
        print(f"🏠 Région: Sidi Yahia du Gharb (zoom 13-14)")

if __name__ == "__main__":
    main()
