#!/usr/bin/env python3
"""
Script d'initialisation de l'index spatial MNT pour C2-EW
À exécuter une fois pour créer l'index des fichiers GeoTIFF
"""

import sys
import os
from pathlib import Path

# Ajouter le chemin du backend au PYTHONPATH
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

# Maintenant on peut importer nos modules
from app.init_mnt_index import main

if __name__ == "__main__":
    print("🗺️ Initialisation de l'index spatial MNT pour C2-EW")
    print("=" * 60)
    
    # Vérifier que le dossier MNT existe
    mnt_dir = backend_path / "app" / "MNT"
    if not mnt_dir.exists():
        print(f"❌ Erreur: Dossier MNT non trouvé: {mnt_dir}")
        print("   Veuillez vous assurer que les fichiers TIFF sont dans backend/app/MNT/")
        sys.exit(1)
    
    # Compter les fichiers TIFF
    tiff_files = list(mnt_dir.glob("*.tif"))
    print(f"📁 Dossier MNT: {mnt_dir}")
    print(f"📊 Fichiers TIFF trouvés: {len(tiff_files)}")
    
    if len(tiff_files) == 0:
        print("❌ Aucun fichier TIFF trouvé dans le dossier MNT")
        sys.exit(1)
    
    # Afficher quelques exemples
    print("📋 Exemples de fichiers:")
    for i, tiff_file in enumerate(tiff_files[:5]):
        size_mb = tiff_file.stat().st_size / (1024 * 1024)
        print(f"   {i+1}. {tiff_file.name} ({size_mb:.1f} MB)")
    
    if len(tiff_files) > 5:
        print(f"   ... et {len(tiff_files) - 5} autres fichiers")
    
    print("\n🚀 Démarrage de l'indexation...")
    
    # Exécuter l'initialisation
    success = main()
    
    if success:
        print("\n✅ Index spatial MNT créé avec succès!")
        print("🎯 L'outil de visibilité optique est maintenant opérationnel")
        print("\n📝 Prochaines étapes:")
        print("   1. Démarrer le backend FastAPI")
        print("   2. Activer l'outil 'Visibilité Optique' dans la sidebar gauche")
        print("   3. Cliquer sur la carte pour définir observateur et cible")
    else:
        print("\n❌ Erreur lors de la création de l'index")
        sys.exit(1)
