import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  User,
  Settings,
  LogOut,
  Shield,
  Clock,
  Wifi,
  WifiOff,
  ChevronDown,
  Map,
  BarChart3,
  Radio,
  Package,
  AlertTriangle
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import { APP_NAME, APP_VERSION } from '@/utils/constants';

const Navbar = () => {
  const { user, logout } = useAuthStore();
  const { isConnected } = useWebSocket();
  const location = useLocation();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [operationalStatus, setOperationalStatus] = useState('OPERATIONAL');

  // Menu de navigation
  const navigationItems = [
    { path: '/map', label: 'Carte', icon: Map },
    { path: '/dashboard', label: 'Dashboard', icon: BarChart3 },
    { path: '/equipment', label: 'Équipements', icon: Radio },
    { path: '/plugins', label: 'Plugins', icon: Package },
    { path: '/alerts', label: 'Alertes', icon: AlertTriangle },
    { path: '/settings', label: 'Paramètres', icon: Settings },
  ];

  // Mise à jour de l'horloge
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Déterminer le statut opérationnel basé sur la connexion
  useEffect(() => {
    if (!isConnected) {
      setOperationalStatus('DEGRADED');
    } else {
      setOperationalStatus('OPERATIONAL');
    }
  }, [isConnected]);

  const handleLogout = async () => {
    console.log('Déconnexion en cours...');
    setShowUserMenu(false);

    try {
      // Appeler la fonction logout du store
      await logout();

      // Double sécurité : nettoyer manuellement
      localStorage.removeItem('auth-storage');
      sessionStorage.clear();

      // Redirection vers la page de connexion avec navigate
      console.log('Redirection vers /login');
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      // Forcer la redirection même en cas d'erreur
      navigate('/login', { replace: true });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'OPERATIONAL':
        return 'text-status-active';
      case 'DEGRADED':
        return 'text-status-standby';
      case 'OFFLINE':
        return 'text-status-offline';
      default:
        return 'text-status-unknown';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Europe/Paris'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <nav className="h-16 bg-c2-black border-b border-c2-gray-300 flex items-center justify-between px-4 relative z-50">
      {/* Logo et titre */}
      <div className="flex items-center space-x-8">
        <div className="flex items-center space-x-2">
          <Shield className="w-8 h-8 text-c2-blue" />
          <div>
            <h1 className="text-lg font-bold text-c2-white">{APP_NAME}</h1>
            <span className="text-xs text-c2-gray-100">v{APP_VERSION}</span>
          </div>
        </div>

        {/* Menu de navigation */}
        <div className="flex items-center space-x-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-c2-blue text-white'
                    : 'text-c2-gray-100 hover:text-c2-white hover:bg-c2-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Statut opérationnel et horodatage central */}
      <div className="flex items-center space-x-8">
        {/* Statut opérationnel */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            operationalStatus === 'OPERATIONAL' ? 'bg-status-active' :
            operationalStatus === 'DEGRADED' ? 'bg-status-standby' :
            'bg-status-offline'
          } pulse-status`} />
          <span className={`font-semibold ${getStatusColor(operationalStatus)}`}>
            {operationalStatus}
          </span>
        </div>

        {/* Connexion WebSocket */}
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-4 h-4 text-status-active" />
          ) : (
            <WifiOff className="w-4 h-4 text-status-offline" />
          )}
          <span className={`text-sm ${isConnected ? 'text-status-active' : 'text-status-offline'}`}>
            {isConnected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}
          </span>
        </div>

        {/* Horodatage */}
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-c2-gray-100" />
          <div className="text-center">
            <div className="text-sm font-mono text-c2-white">
              {formatTime(currentTime)}
            </div>
            <div className="text-xs text-c2-gray-100">
              {formatDate(currentTime)}
            </div>
          </div>
        </div>
      </div>

      {/* Menu utilisateur C2 militaire */}
      <div className="flex items-center space-x-4">
        {/* Bouton Profil */}
        <button
          onClick={() => setShowUserMenu(!showUserMenu)}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-c2-gray-400 hover:bg-c2-gray-300 transition-colors border border-c2-gray-300"
        >
          <div className="w-8 h-8 bg-c2-blue rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div className="hidden md:block text-left">
            <div className="text-sm font-medium text-c2-white">
              {user?.username || 'Opérateur'}
            </div>
            <div className="text-xs text-c2-gray-100">
              {user?.role || 'C2-EW'}
            </div>
          </div>
        </button>

        {/* Bouton Déconnexion direct */}
        <button
          onClick={handleLogout}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-status-offline hover:bg-red-600 transition-colors text-white"
          title="Déconnexion sécurisée"
        >
          <LogOut className="w-4 h-4" />
          <span className="hidden md:block text-sm font-medium">Déconnexion</span>
        </button>

        {/* Panneau profil C2 militaire */}
        {showUserMenu && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="absolute right-4 top-16 w-96 bg-c2-black border-2 border-c2-blue rounded-lg shadow-2xl z-50"
          >
            {/* En-tête C2 */}
            <div className="bg-c2-blue p-4 rounded-t-lg">
              <div className="flex items-center space-x-3">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-c2-blue" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white">
                    {user?.username || 'OPÉRATEUR'}
                  </h3>
                  <p className="text-blue-100 font-medium">
                    {user?.role || 'SYSTÈME C2-EW'} • {user?.unit || 'UNITÉ TACTIQUE'}
                  </p>
                  <div className="flex items-center space-x-1 mt-1">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(operationalStatus)}`}></div>
                    <span className="text-blue-100 text-sm font-medium">{operationalStatus}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Informations système */}
            <div className="p-4 space-y-4">
              {/* Statut connexion */}
              <div className="bg-c2-gray-400 p-3 rounded-lg">
                <h4 className="text-c2-white font-semibold mb-2 flex items-center">
                  <Wifi className="w-4 h-4 mr-2" />
                  STATUT RÉSEAU
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center space-x-2">
                    {isConnected ? (
                      <>
                        <div className="w-2 h-2 bg-status-active rounded-full"></div>
                        <span className="text-status-active font-medium">CONNECTÉ</span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-status-offline rounded-full"></div>
                        <span className="text-status-offline font-medium">DÉCONNECTÉ</span>
                      </>
                    )}
                  </div>
                  <div className="text-c2-gray-100">
                    Serveur: localhost:8000
                  </div>
                </div>
              </div>

              {/* Informations temporelles */}
              <div className="bg-c2-gray-400 p-3 rounded-lg">
                <h4 className="text-c2-white font-semibold mb-2 flex items-center">
                  <Clock className="w-4 h-4 mr-2" />
                  HORODATAGE
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-c2-gray-100">Heure locale:</span>
                    <div className="text-c2-white font-mono font-bold">
                      {formatTime(currentTime)}
                    </div>
                  </div>
                  <div>
                    <span className="text-c2-gray-100">Date:</span>
                    <div className="text-c2-white font-mono font-bold">
                      {formatDate(currentTime)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Informations session */}
              <div className="bg-c2-gray-400 p-3 rounded-lg">
                <h4 className="text-c2-white font-semibold mb-2 flex items-center">
                  <Shield className="w-4 h-4 mr-2" />
                  SESSION SÉCURISÉE
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-c2-gray-100">Niveau d'accès:</span>
                    <span className="text-c2-white font-medium">{user?.role || 'OPÉRATEUR'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-c2-gray-100">Permissions:</span>
                    <span className="text-status-active font-medium">AUTORISÉ</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-c2-gray-100">Dernière activité:</span>
                    <span className="text-c2-white font-mono">{formatTime(currentTime)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="p-4 border-t border-c2-gray-300">
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => {
                    setShowUserMenu(false);
                    navigate('/settings');
                  }}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-c2-gray-300 hover:bg-c2-gray-200 rounded-lg transition-colors text-c2-white"
                >
                  <Settings className="w-4 h-4" />
                  <span className="font-medium">Paramètres</span>
                </button>

                <button
                  onClick={() => {
                    setShowUserMenu(false);
                    handleLogout();
                  }}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-status-offline hover:bg-red-600 rounded-lg transition-colors text-white"
                >
                  <LogOut className="w-4 h-4" />
                  <span className="font-medium">Déconnexion</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Overlay pour fermer le menu */}
        {showUserMenu && (
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowUserMenu(false)}
          />
        )}
      </div>
    </nav>
  );
};

export default Navbar;
