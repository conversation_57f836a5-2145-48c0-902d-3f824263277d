#!/usr/bin/env python3
"""
Script pour télécharger les tuiles zoom 5 pour une vue globale étendue
Vue étendue : Sénégal → Égypte, Océan Atlantique → Moyen-Orient
"""

import os
import requests
import time
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """Convertir lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile"""
    tile_url = base_url.format(z=z, x=x, y=y)
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # C<PERSON>er les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        print(f"✅ Tuile existe déjà: {z}/{x}/{y}")
        return True
    
    try:
        response = session.get(tile_url, timeout=10)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"✅ Téléchargé: {z}/{x}/{y}")
            return True
        else:
            print(f"❌ Erreur {response.status_code}: {z}/{x}/{y}")
            return False
    except Exception as e:
        print(f"❌ Exception: {z}/{x}/{y} - {e}")
        return False

def main():
    # Configuration
    ZOOM = 5
    BASE_URL = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    OUTPUT_DIR = Path("tiles/esri_satellite_morocco")
    
    # Limites géographiques pour vue étendue COMPLÈTE
    # Couvrir TOUT le composant sans carreaux noirs
    SOUTH = 5.0    # Encore plus au sud (Guinée)
    NORTH = 45.0   # Encore plus au nord (France)
    WEST = -35.0   # Encore plus à l'ouest (Océan Atlantique)
    EAST = 40.0    # Encore plus à l'est (Turquie)
    
    print(f"🗺️ TÉLÉCHARGEMENT TUILES ZOOM {ZOOM}")
    print(f"📍 Zone: {SOUTH}°N à {NORTH}°N, {WEST}°E à {EAST}°E")
    
    # Calculer les coordonnées de tuiles
    x_min, y_max = deg2num(SOUTH, WEST, ZOOM)
    x_max, y_min = deg2num(NORTH, EAST, ZOOM)
    
    print(f"📊 Tuiles: X={x_min} à {x_max}, Y={y_min} à {y_max}")
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📈 Total à télécharger: {total_tiles} tuiles")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Télécharger les tuiles
    downloaded = 0
    failed = 0
    
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            success = download_tile(x, y, ZOOM, BASE_URL, OUTPUT_DIR, session)
            if success:
                downloaded += 1
            else:
                failed += 1
            
            # Pause pour éviter la surcharge du serveur
            time.sleep(0.1)
            
            # Afficher le progrès
            if (downloaded + failed) % 10 == 0:
                progress = ((downloaded + failed) / total_tiles) * 100
                print(f"📊 Progrès: {progress:.1f}% ({downloaded} OK, {failed} échecs)")
    
    print(f"\n🎉 TERMINÉ!")
    print(f"✅ Téléchargées: {downloaded}")
    print(f"❌ Échecs: {failed}")
    print(f"📊 Total: {downloaded + failed}/{total_tiles}")
    
    # Remettre les limites dans le code
    print(f"\n🔧 MAINTENANT, remettez ces limites dans MoroccoMap.jsx:")
    print(f"Zoom 5: [{SOUTH}, {WEST}] à [{NORTH}, {EAST}]")

if __name__ == "__main__":
    main()
