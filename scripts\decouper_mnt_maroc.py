#!/usr/bin/env python3
"""
Script de découpage optimal du MNT Maroc 12.6GB pour C2-EW
Crée des tuiles optimisées pour les calculs de visibilité
"""

import os
import sys
import argparse
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import math

# Configuration environnement PROJ
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

import rasterio
from rasterio.windows import Window
from rasterio.warp import calculate_default_transform, reproject, Resampling
from rasterio.crs import CRS
import numpy as np
from tqdm import tqdm

class MNTSlicer:
    """Découpeur optimisé pour MNT Maroc"""
    
    def __init__(self, source_file: str, output_dir: str):
        self.source_file = Path(source_file)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Zones d'intérêt pour le Maroc
        self.zones_maroc = {
            'nord': {'bounds': (-6.5, 34.0, -1.0, 36.0), 'priorite': 1, 'taille': 0.5},
            'centre': {'bounds': (-8.0, 31.0, -4.0, 34.0), 'priorite': 1, 'taille': 0.5},
            'sud': {'bounds': (-10.0, 28.0, -6.0, 31.0), 'priorite': 2, 'taille': 1.0},
            'sahara': {'bounds': (-17.0, 20.5, -8.0, 28.0), 'priorite': 3, 'taille': 2.0},
            'frontiere': {'bounds': (-4.0, 30.0, -1.0, 35.0), 'priorite': 2, 'taille': 0.5}
        }
        
        self.metadata = {
            'tiles_created': 0,
            'total_size_mb': 0,
            'coverage_bounds': None,
            'tiles_info': []
        }
    
    def analyser_source(self) -> Dict:
        """Analyser le fichier source MNT"""
        print(f"📊 Analyse du fichier source: {self.source_file}")
        
        if not self.source_file.exists():
            raise FileNotFoundError(f"Fichier MNT non trouvé: {self.source_file}")
        
        with rasterio.open(self.source_file) as src:
            info = {
                'bounds': src.bounds,
                'crs': src.crs.to_string() if src.crs else 'Unknown',
                'width': src.width,
                'height': src.height,
                'dtype': str(src.dtypes[0]),
                'nodata': src.nodata,
                'resolution': src.res,
                'size_mb': self.source_file.stat().st_size / (1024 * 1024)
            }
            
            print(f"   📏 Dimensions: {info['width']} x {info['height']}")
            print(f"   🗺️  CRS: {info['crs']}")
            print(f"   📐 Résolution: {info['resolution'][0]:.6f}° x {info['resolution'][1]:.6f}°")
            print(f"   💾 Taille: {info['size_mb']:.1f} MB")
            print(f"   🌍 Bounds: {info['bounds']}")
            
            return info
    
    def calculer_tuiles_optimales(self, zone_bounds: Tuple, taille_tuile: float) -> List[Dict]:
        """Calculer les tuiles optimales pour une zone"""
        min_x, min_y, max_x, max_y = zone_bounds
        
        tuiles = []
        
        # Calculer le nombre de tuiles nécessaires
        nb_tuiles_x = math.ceil((max_x - min_x) / taille_tuile)
        nb_tuiles_y = math.ceil((max_y - min_y) / taille_tuile)
        
        for i in range(nb_tuiles_x):
            for j in range(nb_tuiles_y):
                # Coordonnées de la tuile
                tile_min_x = min_x + i * taille_tuile
                tile_max_x = min(tile_min_x + taille_tuile, max_x)
                tile_min_y = min_y + j * taille_tuile
                tile_max_y = min(tile_min_y + taille_tuile, max_y)
                
                # Nom de fichier basé sur les coordonnées
                lat_str = f"n{int(tile_max_y):02d}" if tile_max_y >= 0 else f"s{int(abs(tile_min_y)):02d}"
                lon_str = f"w{int(abs(tile_min_x)):03d}" if tile_min_x < 0 else f"e{int(tile_min_x):03d}"
                
                tuile_info = {
                    'nom': f"{lat_str}_{lon_str}_{taille_tuile}deg.tif",
                    'bounds': (tile_min_x, tile_min_y, tile_max_x, tile_max_y),
                    'taille_deg': taille_tuile
                }
                
                tuiles.append(tuile_info)
        
        return tuiles
    
    def creer_tuile(self, src_dataset, tuile_info: Dict, zone_nom: str) -> bool:
        """Créer une tuile individuelle"""
        try:
            bounds = tuile_info['bounds']
            nom_fichier = tuile_info['nom']
            
            # Créer le dossier de zone
            zone_dir = self.output_dir / f"zone_{zone_nom}"
            zone_dir.mkdir(exist_ok=True)
            
            output_path = zone_dir / nom_fichier
            
            # Calculer la fenêtre dans le fichier source
            window = rasterio.windows.from_bounds(*bounds, src_dataset.transform)
            
            # Lire les données
            data = src_dataset.read(1, window=window)
            
            # Vérifier qu'il y a des données valides
            if data.size == 0 or np.all(data == src_dataset.nodata):
                return False
            
            # Calculer la nouvelle transformation
            height, width = data.shape
            transform = rasterio.transform.from_bounds(*bounds, width, height)
            
            # Profil pour la tuile de sortie (COG optimisé)
            profile = {
                'driver': 'GTiff',
                'height': height,
                'width': width,
                'count': 1,
                'dtype': src_dataset.dtypes[0],
                'crs': src_dataset.crs,
                'transform': transform,
                'nodata': src_dataset.nodata,
                'compress': 'LZW',
                'tiled': True,
                'blockxsize': 512,
                'blockysize': 512,
                'BIGTIFF': 'IF_SAFER'
            }
            
            # Écrire la tuile
            with rasterio.open(output_path, 'w', **profile) as dst:
                dst.write(data, 1)
                
                # Ajouter des pyramides pour l'optimisation
                factors = [2, 4, 8, 16]
                dst.build_overviews(factors, Resampling.average)
                dst.update_tags(ns='rio_overview', resampling='average')
            
            # Mettre à jour les métadonnées
            taille_mb = output_path.stat().st_size / (1024 * 1024)
            self.metadata['tiles_created'] += 1
            self.metadata['total_size_mb'] += taille_mb
            
            tile_metadata = {
                'nom': nom_fichier,
                'zone': zone_nom,
                'bounds': bounds,
                'taille_mb': taille_mb,
                'path': str(output_path.relative_to(self.output_dir))
            }
            self.metadata['tiles_info'].append(tile_metadata)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur création tuile {tuile_info['nom']}: {e}")
            return False
    
    def decouper_par_zones(self) -> bool:
        """Découper le MNT par zones optimisées"""
        print("🔪 Découpage du MNT par zones...")
        
        try:
            with rasterio.open(self.source_file) as src:
                info_source = self.analyser_source()
                
                total_tuiles = 0
                for zone_nom, zone_info in self.zones_maroc.items():
                    tuiles = self.calculer_tuiles_optimales(
                        zone_info['bounds'], 
                        zone_info['taille']
                    )
                    total_tuiles += len(tuiles)
                
                print(f"📊 Total de tuiles à créer: {total_tuiles}")
                
                # Barre de progression globale
                with tqdm(total=total_tuiles, desc="Création tuiles") as pbar:
                    
                    for zone_nom, zone_info in self.zones_maroc.items():
                        print(f"\n🗺️  Zone {zone_nom} (priorité {zone_info['priorite']})")
                        
                        tuiles = self.calculer_tuiles_optimales(
                            zone_info['bounds'], 
                            zone_info['taille']
                        )
                        
                        tuiles_creees = 0
                        for tuile in tuiles:
                            if self.creer_tuile(src, tuile, zone_nom):
                                tuiles_creees += 1
                            pbar.update(1)
                        
                        print(f"   ✅ {tuiles_creees}/{len(tuiles)} tuiles créées")
                
                return True
                
        except Exception as e:
            print(f"❌ Erreur lors du découpage: {e}")
            return False
    
    def sauvegarder_metadata(self):
        """Sauvegarder les métadonnées du découpage"""
        metadata_file = self.output_dir / "metadata_decoupage.json"
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        
        print(f"📋 Métadonnées sauvegardées: {metadata_file}")
        
        # Résumé
        print(f"\n📊 RÉSUMÉ DU DÉCOUPAGE:")
        print(f"   🔢 Tuiles créées: {self.metadata['tiles_created']}")
        print(f"   💾 Taille totale: {self.metadata['total_size_mb']:.1f} MB")
        print(f"   📁 Dossier sortie: {self.output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Découpage optimal MNT Maroc pour C2-EW")
    parser.add_argument("--source", required=True, help="Fichier MNT source (12.6GB)")
    parser.add_argument("--output", required=True, help="Dossier de sortie")
    parser.add_argument("--validate", action="store_true", help="Valider après découpage")
    
    args = parser.parse_args()
    
    print("🗺️ DÉCOUPAGE MNT MAROC POUR C2-EW")
    print("=" * 50)
    
    # Vérifications préliminaires
    source_path = Path(args.source)
    if not source_path.exists():
        print(f"❌ Fichier source non trouvé: {source_path}")
        sys.exit(1)
    
    # Créer le découpeur
    slicer = MNTSlicer(args.source, args.output)
    
    # Exécuter le découpage
    if slicer.decouper_par_zones():
        slicer.sauvegarder_metadata()
        
        if args.validate:
            print("\n🔍 Validation des tuiles créées...")
            # TODO: Ajouter validation
        
        print("\n✅ Découpage terminé avec succès!")
        print("🎯 Prêt pour l'indexation spatiale C2-EW")
        
    else:
        print("\n❌ Échec du découpage")
        sys.exit(1)

if __name__ == "__main__":
    main()
