#!/usr/bin/env python3
"""
Script pour télécharger les tuiles de labels pour la VUE GLOBALE (zoom 5)
pour éviter les zones noires après l'initialisation
"""

import os
import requests
import time
import math
from pathlib import Path

def lat_lon_to_tile(lat, lon, z):
    """Convertir latitude/longitude en coordonnées de tuile"""
    lat_rad = math.radians(lat)
    n = 2.0 ** z
    x = int((lon + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile de labels"""
    tile_url = base_url.format(z=z, x=x, y=y, s='a')
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # Créer les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        return True
    
    try:
        response = session.get(tile_url, timeout=15)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"🔽 Labels Z{z}: {x}/{y}")
            return True
        else:
            return False
    except Exception as e:
        return False

def main():
    # Configuration pour labels
    BASE_URL = "https://{s}.basemaps.cartocdn.com/light_only_labels/{z}/{x}/{y}.png"
    OUTPUT_DIR = Path("tiles/carto_labels")
    
    print(f"🌍 TÉLÉCHARGEMENT LABELS VUE GLOBALE (ZOOM 5)")
    print(f"🎯 Objectif: Éviter les zones noires après initialisation")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # ZOOM 5 : Vue globale étendue
    zoom = 5
    print(f"\n📊 ZOOM {zoom} - VUE GLOBALE")
    
    # Limites géographiques pour vue globale FIXE (Lisbonne/Dakar/Luxor/Océan)
    bounds = {
        'north': 38.8,   # Lisbonne (limite haut)
        'south': 14.7,   # Dakar (limite bas)
        'west': -25.0,   # Océan Atlantique (limite droite)
        'east': 32.6     # Luxor (limite gauche)
    }
    
    print(f"📍 Limites géographiques:")
    print(f"   Nord: {bounds['north']}°, Sud: {bounds['south']}°")
    print(f"   Ouest: {bounds['west']}°, Est: {bounds['east']}°")
    
    # Convertir en coordonnées de tuiles
    x_min, y_min = lat_lon_to_tile(bounds['south'], bounds['west'], zoom)
    x_max, y_max = lat_lon_to_tile(bounds['north'], bounds['east'], zoom)
    
    # S'assurer que les coordonnées sont dans le bon ordre
    if x_min > x_max:
        x_min, x_max = x_max, x_min
    if y_min > y_max:
        y_min, y_max = y_max, y_min
    
    print(f"📈 Tuiles: X={x_min}-{x_max}, Y={y_min}-{y_max}")
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📊 Total tuiles à traiter: {total_tiles}")
    
    downloaded = 0
    existing = 0
    
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            tile_path = OUTPUT_DIR / str(zoom) / str(x) / f"{y}.png"
            
            if tile_path.exists():
                existing += 1
                continue
            
            success = download_tile(x, y, zoom, BASE_URL, OUTPUT_DIR, session)
            if success:
                downloaded += 1
            
            # Pause pour éviter la surcharge
            time.sleep(0.05)
            
            # Afficher le progrès
            if (downloaded + existing) % 50 == 0:
                progress = ((downloaded + existing) / total_tiles) * 100
                print(f"📊 Z{zoom}: {progress:.1f}% ({downloaded} nouveaux, {existing} existants)")
    
    print(f"✅ Zoom {zoom} terminé: {downloaded} nouveaux, {existing} existants")
    
    print(f"\n🎉 LABELS VUE GLOBALE TERMINÉ!")
    print(f"🔽 Total téléchargé: {downloaded}")
    print(f"✅ Total existant: {existing}")
    print(f"📁 Dossier: tiles/carto_labels/")
    
    print(f"\n🌍 RÉSULTAT:")
    print(f"✅ Vue globale (zoom 5) avec labels offline")
    print(f"✅ Pas de zones noires après initialisation")
    print(f"✅ Transition fluide vers zones bloquées")

if __name__ == "__main__":
    main()
