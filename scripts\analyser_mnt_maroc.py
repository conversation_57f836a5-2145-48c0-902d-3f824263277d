#!/usr/bin/env python3
"""
Script d'analyse du fichier MNT Maroc global
Analyse les caractéristiques avant découpage optimal
"""

import os
import sys
from pathlib import Path

# Configuration environnement PROJ
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

# Supprimer les chemins PostgreSQL du PATH
current_path = os.environ.get('PATH', '')
path_parts = []
for part in current_path.split(os.pathsep):
    if 'postgresql' not in part.lower():
        path_parts.append(part)
os.environ['PATH'] = os.pathsep.join(path_parts)

import rasterio
from rasterio.crs import CRS
import numpy as np

def analyser_mnt_maroc():
    """Analyser le fichier MNT Maroc global"""

    # Chemin vers le fichier MNT (chemin absolu)
    base_dir = Path(__file__).parent.parent
    mnt_file = base_dir / "backend" / "app" / "mnt maroc" / "mnt_maroc.tif"
    
    if not mnt_file.exists():
        print(f"❌ Fichier MNT non trouvé: {mnt_file}")
        return False
    
    print("🗺️ ANALYSE DU MNT MAROC GLOBAL")
    print("=" * 50)
    
    try:
        with rasterio.open(mnt_file) as dataset:
            # Informations de base
            print(f"📁 Fichier: {mnt_file}")
            print(f"💾 Taille: {mnt_file.stat().st_size / (1024**3):.2f} GB")
            print()
            
            # Caractéristiques spatiales
            print("📊 CARACTÉRISTIQUES SPATIALES:")
            print(f"   📏 Dimensions: {dataset.width} x {dataset.height} pixels")
            print(f"   🗺️  CRS: {dataset.crs}")
            print(f"   📐 Résolution: {dataset.res[0]:.8f}° x {dataset.res[1]:.8f}°")
            print(f"   🌍 Bounds: {dataset.bounds}")
            print(f"   📊 Type de données: {dataset.dtypes[0]}")
            print(f"   🚫 Valeur NoData: {dataset.nodata}")
            print()
            
            # Calculs de couverture
            bounds = dataset.bounds
            width_deg = bounds.right - bounds.left
            height_deg = bounds.top - bounds.bottom
            
            print("🌍 COUVERTURE GÉOGRAPHIQUE:")
            print(f"   📍 Longitude: {bounds.left:.6f}° à {bounds.right:.6f}° ({width_deg:.6f}°)")
            print(f"   📍 Latitude: {bounds.bottom:.6f}° à {bounds.top:.6f}° ({height_deg:.6f}°)")
            print()
            
            # Résolution en mètres (approximative)
            lat_center = (bounds.top + bounds.bottom) / 2
            meters_per_deg_lat = 111320  # mètres par degré de latitude
            meters_per_deg_lon = 111320 * np.cos(np.radians(lat_center))  # varie avec latitude
            
            res_x_m = dataset.res[0] * meters_per_deg_lon
            res_y_m = abs(dataset.res[1]) * meters_per_deg_lat
            
            print("📐 RÉSOLUTION EN MÈTRES:")
            print(f"   📏 X (longitude): {res_x_m:.2f} m/pixel")
            print(f"   📏 Y (latitude): {res_y_m:.2f} m/pixel")
            print()
            
            # Échantillonnage des données pour statistiques
            print("📊 ANALYSE DES DONNÉES:")
            print("   🔍 Échantillonnage des élévations...")
            
            # Lire un échantillon au centre
            center_x = dataset.width // 2
            center_y = dataset.height // 2
            sample_size = min(1000, dataset.width // 10, dataset.height // 10)
            
            window = rasterio.windows.Window(
                center_x - sample_size//2, 
                center_y - sample_size//2,
                sample_size, 
                sample_size
            )
            
            sample_data = dataset.read(1, window=window)
            
            # Filtrer les valeurs NoData
            valid_data = sample_data[sample_data != dataset.nodata]
            
            if len(valid_data) > 0:
                print(f"   📈 Élévation min: {valid_data.min():.1f} m")
                print(f"   📈 Élévation max: {valid_data.max():.1f} m")
                print(f"   📈 Élévation moyenne: {valid_data.mean():.1f} m")
                print(f"   📈 Écart-type: {valid_data.std():.1f} m")
            else:
                print("   ⚠️  Pas de données valides dans l'échantillon")
            
            print()
            
            # Recommandations de découpage
            print("🎯 RECOMMANDATIONS DE DÉCOUPAGE:")
            
            # Calculer tailles de tuiles optimales
            target_tile_size_mb = 25  # MB par tuile
            bytes_per_pixel = np.dtype(dataset.dtypes[0]).itemsize
            pixels_per_mb = (1024 * 1024) / bytes_per_pixel
            target_pixels_per_tile = target_tile_size_mb * pixels_per_mb
            
            # Taille de tuile en pixels (carré)
            tile_size_pixels = int(np.sqrt(target_pixels_per_tile))
            
            # Taille de tuile en degrés
            tile_size_deg_x = tile_size_pixels * dataset.res[0]
            tile_size_deg_y = tile_size_pixels * abs(dataset.res[1])
            
            # Nombre de tuiles nécessaires
            nb_tiles_x = int(np.ceil(dataset.width / tile_size_pixels))
            nb_tiles_y = int(np.ceil(dataset.height / tile_size_pixels))
            total_tiles = nb_tiles_x * nb_tiles_y
            
            print(f"   🔢 Taille tuile optimale: {tile_size_pixels} x {tile_size_pixels} pixels")
            print(f"   📐 Taille tuile en degrés: {tile_size_deg_x:.4f}° x {tile_size_deg_y:.4f}°")
            print(f"   📊 Nombre de tuiles: {nb_tiles_x} x {nb_tiles_y} = {total_tiles} tuiles")
            print(f"   💾 Taille estimée par tuile: ~{target_tile_size_mb} MB")
            print(f"   💾 Taille totale estimée: ~{total_tiles * target_tile_size_mb / 1024:.1f} GB")
            print()
            
            # Zones prioritaires pour le Maroc
            print("🗺️ ZONES PRIORITAIRES MAROC:")
            zones_maroc = {
                'Nord (Tanger-Tétouan)': (-6.0, 35.0, -4.0, 36.0),
                'Centre (Rabat-Casa)': (-7.5, 33.0, -6.0, 34.5),
                'Atlas (Montagnes)': (-8.0, 31.0, -5.0, 33.0),
                'Sud (Marrakech-Agadir)': (-10.0, 29.0, -7.0, 32.0),
                'Sahara Occidental': (-17.0, 20.5, -8.0, 28.0),
                'Frontière Est': (-4.0, 32.0, -1.0, 35.0)
            }
            
            for zone_nom, (min_x, min_y, max_x, max_y) in zones_maroc.items():
                # Vérifier si la zone est couverte
                if (min_x >= bounds.left and max_x <= bounds.right and 
                    min_y >= bounds.bottom and max_y <= bounds.top):
                    print(f"   ✅ {zone_nom}: Entièrement couverte")
                elif (max_x > bounds.left and min_x < bounds.right and 
                      max_y > bounds.bottom and min_y < bounds.top):
                    print(f"   🔶 {zone_nom}: Partiellement couverte")
                else:
                    print(f"   ❌ {zone_nom}: Non couverte")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return False

def main():
    print("🔍 ANALYSE DU MNT MAROC POUR C2-EW")
    print("Préparation du découpage optimal...")
    print()
    
    if analyser_mnt_maroc():
        print("✅ Analyse terminée!")
        print()
        print("🚀 PROCHAINES ÉTAPES:")
        print("   1. Exécuter le découpage avec: python scripts/decouper_mnt_maroc.py")
        print("   2. Créer l'index spatial avec: python scripts/init_mnt_index.py")
        print("   3. Tester la visibilité optique dans C2-EW")
    else:
        print("❌ Échec de l'analyse")
        sys.exit(1)

if __name__ == "__main__":
    main()
