#!/usr/bin/env python3
"""
Téléchargement de tuiles zoom 9 entre Tindouf et Zaouiet Kounta
Zone spécifique manquante dans la couverture
"""

import os
import requests
import time
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_tile(url, filepath, max_retries=3):
    """Télécharge une tuile avec gestion d'erreurs"""
    if os.path.exists(filepath):
        return False  # Déjà téléchargée
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
            elif response.status_code == 404:
                return False  # Tuile n'existe pas
            else:
                print(f"⚠️  Erreur {response.status_code} pour {url}")
                
        except Exception as e:
            print(f"❌ Erreur téléchargement (tentative {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Backoff exponentiel
    
    return False

def download_zone_tiles(zone_name, bounds, zoom, tile_type="satellite"):
    """Télécharge les tuiles pour une zone spécifique"""
    print(f"\n🗺️  TÉLÉCHARGEMENT ZONE: {zone_name}")
    print(f"📍 Limites: Nord {bounds['north']}°, Sud {bounds['south']}°")
    print(f"📍 Limites: Ouest {bounds['west']}°, Est {bounds['east']}°")
    print(f"🔍 Zoom: {zoom}")
    
    # Calcul des tuiles
    x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
    x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
    
    # Correction si inversé
    if x_min > x_max:
        x_min, x_max = x_max, x_min
    if y_min > y_max:
        y_min, y_max = y_max, y_min
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📊 Tuiles à traiter: {total_tiles}")
    print(f"📈 Tuiles: X={x_min}-{x_max}, Y={y_min}-{y_max}")
    
    # Configuration selon le type
    if tile_type == "satellite":
        base_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile"
        folder = "tiles/esri_satellite_morocco"
    else:  # labels
        base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
        folder = "tiles/carto_labels"
    
    downloaded = 0
    existing = 0
    
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            if tile_type == "satellite":
                url = f"{base_url}/{zoom}/{y}/{x}"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            else:
                url = f"{base_url}/{zoom}/{x}/{y}.png"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            
            print(f"📥 Téléchargement tuile: X={x}, Y={y}")
            
            if download_tile(url, filepath):
                downloaded += 1
                print(f"✅ Nouvelle tuile: {filepath}")
            else:
                existing += 1
                print(f"⏭️  Existante: {filepath}")
            
            time.sleep(0.2)  # Délai pour éviter la surcharge
    
    print(f"✅ Zone {zone_name} terminée:")
    print(f"🔽 Nouvelles: {downloaded}")
    print(f"✅ Existantes: {existing}")
    return downloaded, existing

def main():
    print("🗺️  TÉLÉCHARGEMENT TUILES TINDOUF-ZAOUIET KOUNTA")
    print("🎯 Objectif: Combler la zone manquante entre ces deux villes")
    
    # COORDONNÉES DES VILLES
    tindouf_coords = {"lat": 27.67, "lon": -8.15}  # Tindouf, Algérie
    zaouiet_kounta_coords = {"lat": 25.37, "lon": -9.95}  # Zaouiet Kounta, Mauritanie
    
    print(f"\n📍 VILLES DE RÉFÉRENCE:")
    print(f"🏙️  Tindouf: {tindouf_coords['lat']}°N, {tindouf_coords['lon']}°E")
    print(f"🏙️  Zaouiet Kounta: {zaouiet_kounta_coords['lat']}°N, {zaouiet_kounta_coords['lon']}°E")
    
    # ZONE ENTRE LES DEUX VILLES (avec marge)
    zone_between = {
        "name": "Tindouf-Zaouiet Kounta",
        "bounds": {
            "north": max(tindouf_coords['lat'], zaouiet_kounta_coords['lat']) + 0.5,  # 28.17°N
            "south": min(tindouf_coords['lat'], zaouiet_kounta_coords['lat']) - 0.5,  # 24.87°N
            "west": min(tindouf_coords['lon'], zaouiet_kounta_coords['lon']) - 0.5,   # -10.45°W
            "east": max(tindouf_coords['lon'], zaouiet_kounta_coords['lon']) + 0.5    # -7.65°W
        }
    }
    
    print(f"\n🗺️  ZONE CALCULÉE:")
    print(f"📍 Nord: {zone_between['bounds']['north']}°")
    print(f"📍 Sud: {zone_between['bounds']['south']}°")
    print(f"📍 Ouest: {zone_between['bounds']['west']}°")
    print(f"📍 Est: {zone_between['bounds']['east']}°")
    
    # Calculer la superficie
    area = (zone_between['bounds']['north'] - zone_between['bounds']['south']) * \
           (zone_between['bounds']['east'] - zone_between['bounds']['west'])
    print(f"📏 Superficie: {area:.2f}° carrés")
    
    total_downloaded = 0
    total_existing = 0
    
    print(f"\n{'='*60}")
    
    # Télécharger satellite
    print(f"📡 SATELLITE - {zone_between['name']}")
    sat_down, sat_exist = download_zone_tiles(
        zone_between['name'], 
        zone_between['bounds'], 
        9, 
        "satellite"
    )
    
    # Télécharger labels
    print(f"🏷️  LABELS - {zone_between['name']}")
    lab_down, lab_exist = download_zone_tiles(
        zone_between['name'], 
        zone_between['bounds'], 
        9, 
        "labels"
    )
    
    total_downloaded = sat_down + lab_down
    total_existing = sat_exist + lab_exist
    
    print(f"\n{'='*60}")
    print("🎉 TÉLÉCHARGEMENT TINDOUF-ZAOUIET KOUNTA TERMINÉ!")
    print(f"🔽 Total téléchargé: {total_downloaded}")
    print(f"✅ Total existant: {total_existing}")
    print(f"📁 Dossiers: tiles/esri_satellite_morocco/ et tiles/carto_labels/")
    
    print(f"\n🌍 RÉSULTAT:")
    print(f"✅ Zone entre Tindouf et Zaouiet Kounta couverte")
    print(f"✅ Zoom 9 pour navigation détaillée")
    print(f"✅ Satellite + Labels offline")
    print(f"✅ Continuité géographique assurée")
    
    # Vérification des tuiles spécifiques
    print(f"\n🔍 VÉRIFICATION DES TUILES:")
    zoom = 9
    
    # Tuiles autour de Tindouf
    x_tindouf, y_tindouf = deg2num(tindouf_coords['lat'], tindouf_coords['lon'], zoom)
    print(f"📍 Tindouf: Tuile X={x_tindouf}, Y={y_tindouf}")
    
    # Tuiles autour de Zaouiet Kounta
    x_zaouiet, y_zaouiet = deg2num(zaouiet_kounta_coords['lat'], zaouiet_kounta_coords['lon'], zoom)
    print(f"📍 Zaouiet Kounta: Tuile X={x_zaouiet}, Y={y_zaouiet}")
    
    # Vérifier si les fichiers existent
    tindouf_sat = f"tiles/esri_satellite_morocco/{zoom}/{x_tindouf}/{y_tindouf}.png"
    zaouiet_sat = f"tiles/esri_satellite_morocco/{zoom}/{x_zaouiet}/{y_zaouiet}.png"
    
    print(f"🔍 Tuile Tindouf: {'✅ Existe' if os.path.exists(tindouf_sat) else '❌ Manquante'}")
    print(f"🔍 Tuile Zaouiet Kounta: {'✅ Existe' if os.path.exists(zaouiet_sat) else '❌ Manquante'}")

if __name__ == "__main__":
    main()
