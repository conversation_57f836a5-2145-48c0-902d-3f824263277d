#!/usr/bin/env python3
"""
Script d'initialisation de l'index spatial MNT
Scanne les fichiers GeoTIFF et crée un R-tree pour optimiser les requêtes de visibilité
"""

import os
import pickle
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import structlog

# Configuration de l'environnement PROJ avant d'importer rasterio
os.environ['PROJ_LIB'] = ''  # Laisser GDAL trouver automatiquement
os.environ['GDAL_DATA'] = ''  # Laisser GDAL trouver automatiquement

# Supprimer les chemins PostgreSQL du PATH pour éviter les conflits
current_path = os.environ.get('PATH', '')
path_parts = []
for part in current_path.split(os.pathsep):
    if 'postgresql' not in part.lower() or 'proj' not in part.lower():
        path_parts.append(part)
os.environ['PATH'] = os.pathsep.join(path_parts)

import rasterio
from rasterio.crs import CRS
from rasterio.warp import transform_bounds
from rtree import index

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger()

class MNTIndexer:
    """Gestionnaire d'indexation spatiale pour les fichiers MNT"""

    def __init__(self, mnt_directory: str = None, index_file: str = None):
        if mnt_directory is None:
            # Déterminer automatiquement le chemin du dossier MNT
            current_dir = Path(__file__).parent
            mnt_directory = current_dir / "MNT"
        if index_file is None:
            # Placer l'index dans le même dossier que le script
            current_dir = Path(__file__).parent.parent
            index_file = current_dir / "mnt_index.pkl"
        self.mnt_directory = Path(mnt_directory)
        self.index_file = Path(index_file)
        self.spatial_index = index.Index()
        self.file_metadata: Dict[int, Dict] = {}
        self.file_counter = 0
        
    def scan_mnt_files(self) -> List[Path]:
        """Scanner le dossier MNT pour trouver tous les fichiers TIFF"""
        tiff_files = []
        
        if not self.mnt_directory.exists():
            logger.error(f"Dossier MNT non trouvé: {self.mnt_directory}")
            return tiff_files
            
        # Chercher tous les fichiers .tif
        for tiff_file in self.mnt_directory.glob("*.tif"):
            if tiff_file.is_file() and tiff_file.stat().st_size > 0:
                tiff_files.append(tiff_file)
                
        logger.info(f"Trouvé {len(tiff_files)} fichiers TIFF dans {self.mnt_directory}")
        return tiff_files
    
    def extract_file_bounds(self, tiff_path: Path) -> Optional[Tuple[float, float, float, float]]:
        """Extraire les limites géographiques d'un fichier TIFF"""
        try:
            with rasterio.open(tiff_path) as dataset:
                bounds = dataset.bounds
                crs = dataset.crs

                # Convertir en WGS84 si nécessaire
                if crs and crs != CRS.from_epsg(4326):
                    try:
                        bounds = transform_bounds(crs, CRS.from_epsg(4326), *bounds)
                    except Exception as proj_error:
                        logger.warning(f"Erreur de projection pour {tiff_path}: {proj_error}")
                        # Utiliser les bounds originaux si la transformation échoue
                        bounds = dataset.bounds

                # Retourner (min_x, min_y, max_x, max_y)
                return bounds

        except Exception as e:
            logger.error(f"Erreur lors de la lecture de {tiff_path}: {e}")
            return None
    
    def get_file_metadata(self, tiff_path: Path) -> Optional[Dict]:
        """Extraire les métadonnées complètes d'un fichier TIFF"""
        try:
            with rasterio.open(tiff_path) as dataset:
                metadata = {
                    'path': str(tiff_path),
                    'bounds': dataset.bounds,
                    'crs': dataset.crs.to_string() if dataset.crs else None,
                    'transform': dataset.transform,
                    'width': dataset.width,
                    'height': dataset.height,
                    'dtype': str(dataset.dtypes[0]),
                    'nodata': dataset.nodata,
                    'resolution': (abs(dataset.transform[0]), abs(dataset.transform[4]))
                }
                
                # Convertir les bounds en WGS84 pour l'index
                if dataset.crs and dataset.crs != CRS.from_epsg(4326):
                    try:
                        wgs84_bounds = transform_bounds(
                            dataset.crs, CRS.from_epsg(4326), *dataset.bounds
                        )
                        metadata['wgs84_bounds'] = wgs84_bounds
                    except Exception as proj_error:
                        logger.warning(f"Erreur de projection pour {tiff_path}: {proj_error}")
                        # Utiliser les bounds originaux si la transformation échoue
                        metadata['wgs84_bounds'] = dataset.bounds
                else:
                    metadata['wgs84_bounds'] = dataset.bounds
                    
                return metadata
                
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des métadonnées de {tiff_path}: {e}")
            return None
    
    def build_spatial_index(self) -> bool:
        """Construire l'index spatial R-tree"""
        logger.info("Construction de l'index spatial R-tree...")
        
        tiff_files = self.scan_mnt_files()
        if not tiff_files:
            logger.error("Aucun fichier TIFF trouvé")
            return False
        
        successful_files = 0
        
        for tiff_file in tiff_files:
            metadata = self.get_file_metadata(tiff_file)
            if metadata:
                # Utiliser les bounds WGS84 pour l'index
                bounds = metadata['wgs84_bounds']
                
                # Ajouter à l'index spatial (min_x, min_y, max_x, max_y)
                self.spatial_index.insert(self.file_counter, bounds)
                
                # Stocker les métadonnées
                self.file_metadata[self.file_counter] = metadata
                
                logger.debug(f"Indexé: {tiff_file.name} - Bounds: {bounds}")
                self.file_counter += 1
                successful_files += 1
            else:
                logger.warning(f"Impossible d'indexer: {tiff_file}")
        
        logger.info(f"Index spatial créé avec {successful_files} fichiers")
        return successful_files > 0
    
    def save_index(self) -> bool:
        """Sauvegarder l'index sur disque"""
        try:
            index_data = {
                'file_metadata': self.file_metadata,
                'file_counter': self.file_counter,
                'mnt_directory': str(self.mnt_directory)
            }
            
            # Sauvegarder les métadonnées
            with open(self.index_file, 'wb') as f:
                pickle.dump(index_data, f)
            
            # Sauvegarder l'index spatial séparément
            spatial_index_file = self.index_file.with_suffix('.idx')
            self.spatial_index.close()
            
            # Recréer l'index et le sauvegarder
            self.spatial_index = index.Index(str(spatial_index_file))
            for file_id, metadata in self.file_metadata.items():
                bounds = metadata['wgs84_bounds']
                self.spatial_index.insert(file_id, bounds)
            
            logger.info(f"Index sauvegardé: {self.index_file}")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde: {e}")
            return False
    
    def load_index(self) -> bool:
        """Charger l'index depuis le disque"""
        try:
            if not self.index_file.exists():
                logger.warning(f"Fichier d'index non trouvé: {self.index_file}")
                return False
            
            # Charger les métadonnées
            with open(self.index_file, 'rb') as f:
                index_data = pickle.load(f)
            
            self.file_metadata = index_data['file_metadata']
            self.file_counter = index_data['file_counter']
            
            # Charger l'index spatial
            spatial_index_file = self.index_file.with_suffix('.idx')
            if spatial_index_file.exists():
                self.spatial_index = index.Index(str(spatial_index_file))
                logger.info(f"Index chargé: {len(self.file_metadata)} fichiers")
                return True
            else:
                logger.warning("Fichier d'index spatial non trouvé")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors du chargement: {e}")
            return False
    
    def query_files_for_bounds(self, min_x: float, min_y: float, max_x: float, max_y: float) -> List[Dict]:
        """Interroger l'index pour trouver les fichiers intersectant une zone"""
        intersecting_files = []
        
        # Requête spatiale
        file_ids = list(self.spatial_index.intersection((min_x, min_y, max_x, max_y)))
        
        for file_id in file_ids:
            if file_id in self.file_metadata:
                intersecting_files.append(self.file_metadata[file_id])
        
        return intersecting_files
    
    def get_stats(self) -> Dict:
        """Obtenir les statistiques de l'index"""
        return {
            'total_files': len(self.file_metadata),
            'index_file': str(self.index_file),
            'mnt_directory': str(self.mnt_directory),
            'index_exists': self.index_file.exists()
        }

def main():
    """Fonction principale d'initialisation"""
    logger.info("🗺️ Initialisation de l'index spatial MNT...")
    
    indexer = MNTIndexer()
    
    # Construire l'index
    if indexer.build_spatial_index():
        # Sauvegarder
        if indexer.save_index():
            stats = indexer.get_stats()
            logger.info("✅ Index spatial MNT créé avec succès!")
            logger.info(f"📊 Statistiques: {stats}")
        else:
            logger.error("❌ Erreur lors de la sauvegarde")
            return False
    else:
        logger.error("❌ Erreur lors de la construction de l'index")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
