#!/usr/bin/env python3
"""
Script pour télécharger TOUTES les tuiles zoom 5 nécessaires
pour une vue globale complète sans carreaux noirs
"""

import os
import requests
import time
import math
from pathlib import Path

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile"""
    tile_url = base_url.format(z=z, x=x, y=y)
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # Créer les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        print(f"✅ Existe: {z}/{x}/{y}")
        return True
    
    try:
        response = session.get(tile_url, timeout=15)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"🔽 Téléchargé: {z}/{x}/{y}")
            return True
        else:
            print(f"❌ Erreur {response.status_code}: {z}/{x}/{y}")
            return False
    except Exception as e:
        print(f"❌ Exception: {z}/{x}/{y} - {e}")
        return False

def main():
    # Configuration
    ZOOM = 5
    BASE_URL = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    OUTPUT_DIR = Path("tiles/esri_satellite_morocco")
    
    print(f"🗺️ TÉLÉCHARGEMENT COMPLET ZOOM {ZOOM}")
    
    # Pour zoom 5, il y a 32x32 = 1024 tuiles au total dans le monde
    # Zone COMPLÈTE : Océan Atlantique → Égypte

    # Zone MAXIMALE pour vue globale parfaite
    X_MIN = 6   # TRÈS à l'ouest (Océan Atlantique profond)
    X_MAX = 25  # TRÈS à l'est (Égypte + Moyen-Orient)
    Y_MIN = 6   # TRÈS au nord (Scandinavie)
    Y_MAX = 20  # TRÈS au sud (Afrique centrale)
    
    print(f"📊 Zone: X={X_MIN} à {X_MAX}, Y={Y_MIN} à {Y_MAX}")
    
    total_tiles = (X_MAX - X_MIN + 1) * (Y_MAX - Y_MIN + 1)
    print(f"📈 Total à télécharger: {total_tiles} tuiles")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Télécharger les tuiles
    downloaded = 0
    failed = 0
    existing = 0
    
    for x in range(X_MIN, X_MAX + 1):
        for y in range(Y_MIN, Y_MAX + 1):
            tile_path = OUTPUT_DIR / str(ZOOM) / str(x) / f"{y}.png"
            
            if tile_path.exists():
                existing += 1
                if existing % 20 == 0:
                    print(f"✅ {existing} tuiles existent déjà...")
                continue
            
            success = download_tile(x, y, ZOOM, BASE_URL, OUTPUT_DIR, session)
            if success:
                downloaded += 1
            else:
                failed += 1
            
            # Pause pour éviter la surcharge du serveur
            time.sleep(0.2)
            
            # Afficher le progrès
            if (downloaded + failed) % 5 == 0:
                total_processed = downloaded + failed + existing
                progress = (total_processed / total_tiles) * 100
                print(f"📊 Progrès: {progress:.1f}% ({downloaded} nouveaux, {existing} existants, {failed} échecs)")
    
    print(f"\n🎉 TERMINÉ!")
    print(f"🔽 Nouvelles tuiles: {downloaded}")
    print(f"✅ Tuiles existantes: {existing}")
    print(f"❌ Échecs: {failed}")
    print(f"📊 Total: {downloaded + existing + failed}/{total_tiles}")
    
    print(f"\n🔧 Maintenant vous devriez avoir une vue globale complète en zoom 5!")

if __name__ == "__main__":
    main()
