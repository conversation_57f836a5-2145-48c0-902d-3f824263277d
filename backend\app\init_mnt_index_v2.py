#!/usr/bin/env python3
"""
Script d'initialisation de l'index R-tree pour MNT
CONFORME AUX SPÉCIFICATIONS C2-EW
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Configuration environnement
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

try:
    import rasterio
    from rasterio.crs import CRS
    from rtree import index
    import pyproj
    DEPENDENCIES_OK = True
    print("✅ Toutes les dépendances disponibles")
except ImportError as e:
    print(f"❌ Dépendances manquantes: {e}")
    print("💡 Installez: pip install rasterio rtree pyproj")
    DEPENDENCIES_OK = False

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MNTIndexBuilder:
    """Constructeur d'index spatial R-tree CONFORME aux spécifications"""
    
    def __init__(self, mnt_directory: str = None):
        if mnt_directory is None:
            self.mnt_directory = Path(__file__).parent / "MNT"
        else:
            self.mnt_directory = Path(mnt_directory)
        
        # Fichiers de sortie conformes aux spécifications
        self.index_file = Path(__file__).parent / "mnt_spatial_index"
        self.metadata_file = Path(__file__).parent / "mnt_metadata.json"
        
        # Index spatial R-tree et métadonnées
        self.spatial_index = index.Index()
        self.tiles_metadata = {}
        self.tile_counter = 0
        
        logger.info(f"📁 Dossier MNT: {self.mnt_directory}")
        logger.info(f"📋 Index R-tree: {self.index_file}")
        logger.info(f"📋 Métadonnées: {self.metadata_file}")
    
    def scan_geotiff_files(self) -> List[Path]:
        """Scanner tous les fichiers GeoTIFF dans /app/MNT/"""
        if not self.mnt_directory.exists():
            logger.error(f"Dossier MNT non trouvé: {self.mnt_directory}")
            return []
        
        geotiff_files = []
        for pattern in ["*.tif", "*.tiff", "*.TIF", "*.TIFF"]:
            geotiff_files.extend(self.mnt_directory.glob(pattern))
        
        logger.info(f"📊 Fichiers GeoTIFF trouvés: {len(geotiff_files)}")
        return sorted(geotiff_files)
    
    def extract_tile_extent_and_crs(self, tiff_path: Path) -> Optional[Dict]:
        """Extraire l'extent (bounding box) et CRS de chaque GeoTIFF"""
        try:
            with rasterio.open(tiff_path) as dataset:
                # Bounds dans le CRS natif
                bounds_native = dataset.bounds
                
                # CRS de la tuile
                crs = dataset.crs
                crs_string = crs.to_string() if crs else 'EPSG:4326'
                
                # Transformer vers WGS84 pour l'index spatial R-tree
                if crs and crs != CRS.from_epsg(4326):
                    transformer = pyproj.Transformer.from_crs(
                        crs, CRS.from_epsg(4326), always_xy=True
                    )
                    # Transformer les coins de la bounding box
                    min_x, min_y = transformer.transform(bounds_native.left, bounds_native.bottom)
                    max_x, max_y = transformer.transform(bounds_native.right, bounds_native.top)
                    bounds_wgs84 = (min_x, min_y, max_x, max_y)
                else:
                    bounds_wgs84 = bounds_native
                
                # Métadonnées complètes selon spécifications
                metadata = {
                    'file_path': str(tiff_path),
                    'file_name': tiff_path.name,
                    'file_size_mb': tiff_path.stat().st_size / (1024 * 1024),
                    
                    # Extent (bounding box) - CRITIQUE pour R-tree
                    'bounds_native': bounds_native,
                    'bounds_wgs84': bounds_wgs84,
                    'crs': crs_string,
                    
                    # Propriétés raster
                    'width': dataset.width,
                    'height': dataset.height,
                    'count': dataset.count,
                    'dtype': str(dataset.dtypes[0]),
                    'nodata': dataset.nodata,
                    'resolution': dataset.res,
                    'transform': list(dataset.transform),
                    
                    # Géométrie pour requêtes
                    'center_lon': (bounds_wgs84[0] + bounds_wgs84[2]) / 2,
                    'center_lat': (bounds_wgs84[1] + bounds_wgs84[3]) / 2,
                    'area_deg2': (bounds_wgs84[2] - bounds_wgs84[0]) * (bounds_wgs84[3] - bounds_wgs84[1])
                }
                
                return metadata
                
        except Exception as e:
            logger.warning(f"Erreur lecture {tiff_path.name}: {e}")
            return None
    
    def build_rtree_index(self) -> bool:
        """Construire l'index spatial R-tree selon spécifications"""
        logger.info("🔨 Construction de l'index spatial R-tree...")
        
        geotiff_files = self.scan_geotiff_files()
        if not geotiff_files:
            logger.error("❌ Aucun fichier GeoTIFF trouvé")
            return False
        
        indexed_count = 0
        total_size_mb = 0
        crs_stats = {}
        
        for tiff_file in geotiff_files:
            metadata = self.extract_tile_extent_and_crs(tiff_file)
            
            if metadata:
                # Ajouter à l'index spatial R-tree (utilise bounds WGS84)
                bounds_wgs84 = metadata['bounds_wgs84']
                self.spatial_index.insert(self.tile_counter, bounds_wgs84)
                
                # Stocker les métadonnées indexées par ID
                self.tiles_metadata[self.tile_counter] = metadata
                
                # Statistiques
                indexed_count += 1
                total_size_mb += metadata['file_size_mb']
                
                crs = metadata['crs']
                crs_stats[crs] = crs_stats.get(crs, 0) + 1
                
                logger.debug(f"✅ Indexé: {tiff_file.name} ({metadata['file_size_mb']:.1f}MB, {crs})")
                self.tile_counter += 1
            else:
                logger.warning(f"⚠️ Ignoré: {tiff_file.name}")
        
        # Résumé conforme aux spécifications
        logger.info(f"\n📊 INDEX SPATIAL R-TREE CONSTRUIT:")
        logger.info(f"   ✅ Tuiles indexées: {indexed_count}")
        logger.info(f"   💾 Taille totale: {total_size_mb:.1f} MB")
        logger.info(f"   📊 Taille moyenne: {total_size_mb/indexed_count:.1f} MB/tuile")
        
        logger.info(f"\n🗺️ SYSTÈMES DE COORDONNÉES DÉTECTÉS:")
        for crs, count in sorted(crs_stats.items()):
            logger.info(f"   📍 {crs}: {count} tuiles")
        
        return indexed_count > 0
    
    def save_rtree_and_metadata(self) -> bool:
        """Sauvegarder l'index R-tree et métadonnées (pickle ou SQLite)"""
        try:
            logger.info("💾 Sauvegarde de l'index spatial R-tree...")
            
            # Sauvegarder l'index spatial R-tree
            self.spatial_index.close()
            
            # Sauvegarder les métadonnées en JSON (plus lisible que pickle)
            metadata_export = {
                'index_info': {
                    'tiles_count': len(self.tiles_metadata),
                    'total_size_mb': sum(m['file_size_mb'] for m in self.tiles_metadata.values()),
                    'coverage_bounds_wgs84': self._calculate_global_bounds(),
                    'crs_distribution': self._get_crs_distribution(),
                    'created_timestamp': str(Path(__file__).stat().st_mtime)
                },
                'tiles': self.tiles_metadata
            }
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_export, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Index R-tree sauvegardé: {self.index_file}.*")
            logger.info(f"✅ Métadonnées sauvegardées: {self.metadata_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def _calculate_global_bounds(self) -> Tuple[float, float, float, float]:
        """Calculer les bounds globaux WGS84 de toutes les tuiles"""
        if not self.tiles_metadata:
            return (0, 0, 0, 0)
        
        all_bounds = [m['bounds_wgs84'] for m in self.tiles_metadata.values()]
        
        min_lons = [b[0] for b in all_bounds]
        min_lats = [b[1] for b in all_bounds]
        max_lons = [b[2] for b in all_bounds]
        max_lats = [b[3] for b in all_bounds]
        
        return (min(min_lons), min(min_lats), max(max_lons), max(max_lats))
    
    def _get_crs_distribution(self) -> Dict[str, int]:
        """Obtenir la distribution des CRS"""
        crs_count = {}
        for metadata in self.tiles_metadata.values():
            crs = metadata['crs']
            crs_count[crs] = crs_count.get(crs, 0) + 1
        return crs_count
    
    def test_spatial_queries(self):
        """Tester les requêtes spatiales R-tree"""
        logger.info("\n🧪 Test des requêtes spatiales R-tree...")
        
        # Recréer l'index pour les tests
        self.spatial_index = index.Index(str(self.index_file))
        
        test_points = [
            ("Rabat", -6.8498, 33.9716),
            ("Casablanca", -7.6164, 33.5731),
            ("Marrakech", -7.9811, 31.6295),
            ("Agadir", -9.5981, 30.4278),
            ("Laâyoune", -13.2033, 27.1253)
        ]
        
        for name, lon, lat in test_points:
            # Requête ponctuelle dans l'index R-tree
            tile_ids = list(self.spatial_index.intersection((lon, lat, lon, lat)))
            
            if tile_ids:
                tile_id = tile_ids[0]
                metadata = self.tiles_metadata[tile_id]
                logger.info(f"   📍 {name} ({lon:.4f}, {lat:.4f}): {metadata['file_name']}")
            else:
                logger.warning(f"   ❌ {name} ({lon:.4f}, {lat:.4f}): Aucune tuile trouvée")

def main():
    """Fonction principale conforme aux spécifications"""
    if not DEPENDENCIES_OK:
        logger.error("❌ Dépendances manquantes. Installation requise.")
        return False
    
    logger.info("🗺️ INITIALISATION INDEX SPATIAL R-TREE MNT")
    logger.info("CONFORME AUX SPÉCIFICATIONS C2-EW")
    logger.info("=" * 60)
    
    # Créer le constructeur d'index
    builder = MNTIndexBuilder()
    
    # Construire l'index R-tree
    if builder.build_rtree_index():
        # Sauvegarder (pickle ou SQLite)
        if builder.save_rtree_and_metadata():
            builder.test_spatial_queries()
            
            logger.info("\n✅ INDEX SPATIAL R-TREE INITIALISÉ AVEC SUCCÈS!")
            logger.info("🎯 Système de visibilité optique prêt selon spécifications!")
            logger.info("\n🚀 PROCHAINES ÉTAPES:")
            logger.info("   1. Implémenter /app/api/line_of_sight.py")
            logger.info("   2. Tester l'endpoint POST /api/line-of-sight")
            logger.info("   3. Intégrer les snippets React")
            return True
        else:
            logger.error("❌ Erreur lors de la sauvegarde")
            return False
    else:
        logger.error("❌ Erreur lors de la construction de l'index")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
