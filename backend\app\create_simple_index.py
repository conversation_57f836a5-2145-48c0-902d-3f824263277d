#!/usr/bin/env python3
"""
Script simple pour créer un index MNT de base pour tester l'application
"""

import pickle
from pathlib import Path
from rtree import index

def create_simple_index():
    """Créer un index simple pour tester"""
    
    # Créer un index spatial vide
    spatial_index = index.Index()
    
    # Métadonnées fictives pour le Maroc (zone approximative)
    file_metadata = {
        0: {
            'path': 'backend/app/MNT/morocco_sample.tif',
            'bounds': (-13.0, 20.0, -1.0, 36.0),  # Maroc approximatif
            'wgs84_bounds': (-13.0, 20.0, -1.0, 36.0),
            'crs': 'EPSG:4326',
            'width': 1000,
            'height': 1000,
            'dtype': 'float32',
            'nodata': -9999.0,
            'resolution': (0.01, 0.01)
        }
    }
    
    # Ajouter à l'index spatial
    bounds = file_metadata[0]['wgs84_bounds']
    spatial_index.insert(0, bounds)
    
    # Sauvegarder les métadonnées
    index_file = Path('backend/app/mnt_index.pkl')
    index_data = {
        'file_metadata': file_metadata,
        'file_counter': 1,
        'mnt_directory': 'backend/app/MNT'
    }
    
    with open(index_file, 'wb') as f:
        pickle.dump(index_data, f)
    
    # Sauvegarder l'index spatial
    spatial_index_file = index_file.with_suffix('.idx')
    spatial_index.close()
    
    # Recréer et sauvegarder
    spatial_index = index.Index(str(spatial_index_file))
    spatial_index.insert(0, bounds)
    spatial_index.close()
    
    print("✅ Index simple créé pour tester l'application")
    print(f"📁 Fichier index: {index_file}")
    print(f"📁 Fichier spatial: {spatial_index_file}")

if __name__ == "__main__":
    create_simple_index()
