#!/usr/bin/env python3
"""
Script pour télécharger les tuiles de labels (noms de villes) offline
pour TOUS les zooms (6-13) dans les zones détaillées
"""

import os
import requests
import time
import math
from pathlib import Path

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile de labels"""
    tile_url = base_url.format(z=z, x=x, y=y, s='a')
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # Créer les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        return True
    
    try:
        response = session.get(tile_url, timeout=15)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"🔽 Labels Z{z}: {x}/{y}")
            return True
        else:
            return False
    except Exception as e:
        return False

def get_tile_bounds_for_zoom(zoom):
    """Définir les limites de tuiles selon le zoom"""
    if zoom <= 6:
        # Zoom 6 : Zone étendue Maroc + Dakhla + Alger
        return {
            'x_min': 30, 'x_max': 35,
            'y_min': 18, 'y_max': 25
        }
    elif zoom <= 8:
        # Zoom 7-8 : Zone Maroc + Dakhla + Nouadhibou
        return {
            'x_min': 60, 'x_max': 70,
            'y_min': 36, 'y_max': 50
        }
    elif zoom <= 10:
        # Zoom 9-10 : Zone Maroc détaillée + Dakhla
        return {
            'x_min': 120, 'x_max': 140,
            'y_min': 72, 'y_max': 100
        }
    elif zoom <= 12:
        # Zoom 11-12 : Zone Maroc très détaillée
        return {
            'x_min': 240, 'x_max': 280,
            'y_min': 144, 'y_max': 200
        }
    else:
        # Zoom 13+ : Zone Maroc ultra détaillée
        return {
            'x_min': 480, 'x_max': 560,
            'y_min': 288, 'y_max': 400
        }

def main():
    # Configuration pour labels
    BASE_URL = "https://{s}.basemaps.cartocdn.com/light_only_labels/{z}/{x}/{y}.png"
    OUTPUT_DIR = Path("tiles/carto_labels")
    
    print(f"🏷️ TÉLÉCHARGEMENT LABELS TOUS ZOOMS (6-13)")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    total_downloaded = 0
    total_existing = 0
    
    # Télécharger pour zooms 6 à 13
    for zoom in range(6, 14):
        print(f"\n📊 ZOOM {zoom}")
        
        bounds = get_tile_bounds_for_zoom(zoom)
        x_min, x_max = bounds['x_min'], bounds['x_max']
        y_min, y_max = bounds['y_min'], bounds['y_max']
        
        total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
        print(f"📈 Zone: X={x_min}-{x_max}, Y={y_min}-{y_max} ({total_tiles} tuiles)")
        
        downloaded = 0
        existing = 0
        
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                tile_path = OUTPUT_DIR / str(zoom) / str(x) / f"{y}.png"
                
                if tile_path.exists():
                    existing += 1
                    continue
                
                success = download_tile(x, y, zoom, BASE_URL, OUTPUT_DIR, session)
                if success:
                    downloaded += 1
                
                # Pause pour éviter la surcharge
                time.sleep(0.05)
                
                # Afficher le progrès
                if (downloaded + existing) % 50 == 0:
                    progress = ((downloaded + existing) / total_tiles) * 100
                    print(f"📊 Z{zoom}: {progress:.1f}% ({downloaded} nouveaux, {existing} existants)")
        
        print(f"✅ Zoom {zoom} terminé: {downloaded} nouveaux, {existing} existants")
        total_downloaded += downloaded
        total_existing += existing
    
    print(f"\n🎉 LABELS TOUS ZOOMS TERMINÉ!")
    print(f"🔽 Total téléchargé: {total_downloaded}")
    print(f"✅ Total existant: {total_existing}")
    print(f"📁 Dossier: tiles/carto_labels/")

if __name__ == "__main__":
    main()
