#!/usr/bin/env python3
"""
Téléchargement MASSIF de tuiles zoom 9 pour zones sud spécifiques :
- À gauche d'El B<PERSON>h, <PERSON>, Adrar
- En bas de Tamamt
- Labels pour Mahbes, Farsia, Aqqa et toutes petites villes sud
- Frontières Mauritanie et Algérie
"""

import os
import requests
import time
import math
from pathlib import Path
import concurrent.futures
from threading import Lock

# Verrous pour thread safety
download_lock = Lock()
stats_lock = Lock()

# Statistiques globales
global_stats = {
    'downloaded': 0,
    'existing': 0,
    'errors': 0,
    'total': 0
}

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_single_tile(args):
    """Télécharge une seule tuile (pour threading)"""
    url, filepath, tile_type = args
    
    if os.path.exists(filepath):
        with stats_lock:
            global_stats['existing'] += 1
        return False  # Déjà téléchargée
    
    max_retries = 2
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                with download_lock:
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                
                with stats_lock:
                    global_stats['downloaded'] += 1
                    if global_stats['downloaded'] % 50 == 0:
                        print(f"📥 {tile_type}: {global_stats['downloaded']} téléchargées, {global_stats['existing']} existantes")
                
                return True
            elif response.status_code == 404:
                return False  # Tuile n'existe pas
                
        except Exception as e:
            if attempt == max_retries - 1:
                with stats_lock:
                    global_stats['errors'] += 1
            time.sleep(0.5)
    
    return False

def download_zone_massive(zone_name, bounds, zoom, tile_type="satellite", max_workers=8):
    """Télécharge massivement les tuiles pour une zone avec threading"""
    print(f"\n🗺️  TÉLÉCHARGEMENT MASSIF: {zone_name}")
    print(f"📍 Limites: Nord {bounds['north']}°, Sud {bounds['south']}°")
    print(f"📍 Limites: Ouest {bounds['west']}°, Est {bounds['east']}°")
    print(f"🔍 Zoom: {zoom}, Type: {tile_type}")
    
    # Calcul des tuiles
    x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
    x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
    
    # Correction si inversé
    if x_min > x_max:
        x_min, x_max = x_max, x_min
    if y_min > y_max:
        y_min, y_max = y_max, y_min
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📊 Tuiles à traiter: {total_tiles}")
    print(f"📈 Tuiles: X={x_min}-{x_max}, Y={y_min}-{y_max}")
    
    # Configuration selon le type
    if tile_type == "satellite":
        base_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile"
        folder = "tiles/esri_satellite_morocco"
    else:  # labels
        base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
        folder = "tiles/carto_labels"
    
    # Préparer les tâches de téléchargement
    download_tasks = []
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            if tile_type == "satellite":
                url = f"{base_url}/{zoom}/{y}/{x}"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            else:
                url = f"{base_url}/{zoom}/{x}/{y}.png"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            
            download_tasks.append((url, filepath, tile_type))
    
    # Téléchargement parallèle
    start_downloaded = global_stats['downloaded']
    start_existing = global_stats['existing']
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        list(executor.map(download_single_tile, download_tasks))
    
    zone_downloaded = global_stats['downloaded'] - start_downloaded
    zone_existing = global_stats['existing'] - start_existing
    
    print(f"✅ Zone {zone_name} terminée:")
    print(f"🔽 Nouvelles: {zone_downloaded}")
    print(f"✅ Existantes: {zone_existing}")
    
    return zone_downloaded, zone_existing

def main():
    print("🗺️  TÉLÉCHARGEMENT MASSIF TUILES ZOOM 9 - ZONES SUD")
    print("🎯 Objectif: Couverture maximale zones sud + frontières")
    
    # ZONES MASSIVES À TÉLÉCHARGER
    zones_massives = {
        "El_Bioudh_Ouest_Massif": {
            "name": "À gauche d'El Bioudh - MASSIF",
            "bounds": {
                "north": 35.5,   # Nord étendu
                "south": 32.5,   # Sud étendu
                "west": -2.0,    # Très à gauche (ouest)
                "east": 2.5      # Jusqu'à El Bioudh + marge
            }
        },
        "Ain_Sefra_Ouest_Massif": {
            "name": "À gauche d'Ain Sefra - MASSIF",
            "bounds": {
                "north": 33.5,   # Nord étendu
                "south": 30.5,   # Sud étendu
                "west": -3.0,    # Très à gauche (ouest)
                "east": 1.0      # Jusqu'à Ain Sefra + marge
            }
        },
        "Adrar_Ouest_Massif": {
            "name": "À gauche d'Adrar - MASSIF",
            "bounds": {
                "north": 29.0,   # Nord étendu
                "south": 26.0,   # Sud étendu
                "west": -2.5,    # Très à gauche (ouest)
                "east": 1.0      # Jusqu'à Adrar + marge
            }
        },
        "Tamamt_Sud_Massif": {
            "name": "En bas de Tamamt - MASSIF",
            "bounds": {
                "north": 26.5,   # Tamamt + marge
                "south": 23.0,   # Très en bas (sud)
                "west": -3.0,    # Ouest étendu
                "east": 1.0      # Est étendu
            }
        },
        "Frontiere_Mauritanie_Algerie": {
            "name": "Frontières Mauritanie-Algérie",
            "bounds": {
                "north": 27.0,   # Zone frontière nord
                "south": 20.0,   # Zone frontière sud
                "west": -4.0,    # Mauritanie étendue
                "east": 2.0      # Algérie étendue
            }
        },
        "Zone_Sud_Complete": {
            "name": "Zone Sud Complète (Mahbes, Farsia, Aqqa)",
            "bounds": {
                "north": 25.0,   # Nord zone sud
                "south": 19.0,   # Sud extrême
                "west": -6.0,    # Ouest Mauritanie
                "east": 0.0      # Est Algérie
            }
        }
    }
    
    print(f"\n📊 ZONES À TÉLÉCHARGER: {len(zones_massives)}")
    for zone_id, zone_config in zones_massives.items():
        bounds = zone_config['bounds']
        area = (bounds['north'] - bounds['south']) * (bounds['east'] - bounds['west'])
        print(f"🗺️  {zone_config['name']}: {area:.1f}° carrés")
    
    total_downloaded = 0
    total_existing = 0
    
    # Télécharger pour chaque zone
    for zone_id, zone_config in zones_massives.items():
        print(f"\n{'='*80}")
        
        # Satellite
        print(f"📡 SATELLITE - {zone_config['name']}")
        sat_down, sat_exist = download_zone_massive(
            zone_config['name'], 
            zone_config['bounds'], 
            9, 
            "satellite",
            max_workers=6  # Parallélisme modéré
        )
        
        # Labels
        print(f"🏷️  LABELS - {zone_config['name']}")
        lab_down, lab_exist = download_zone_massive(
            zone_config['name'], 
            zone_config['bounds'], 
            9, 
            "labels",
            max_workers=6
        )
        
        total_downloaded += sat_down + lab_down
        total_existing += sat_exist + lab_exist
        
        # Pause entre zones pour éviter la surcharge
        time.sleep(2)
    
    print(f"\n{'='*80}")
    print("🎉 TÉLÉCHARGEMENT MASSIF TERMINÉ!")
    print(f"🔽 Total téléchargé: {global_stats['downloaded']}")
    print(f"✅ Total existant: {global_stats['existing']}")
    print(f"❌ Total erreurs: {global_stats['errors']}")
    print(f"📁 Dossiers: tiles/esri_satellite_morocco/ et tiles/carto_labels/")
    
    print(f"\n🌍 COUVERTURE OBTENUE:")
    print(f"✅ El Bioudh, Ain Sefra, Adrar - zones ouest étendues")
    print(f"✅ Tamamt - zone sud étendue")
    print(f"✅ Frontières Mauritanie-Algérie complètes")
    print(f"✅ Mahbes, Farsia, Aqqa et petites villes sud")
    print(f"✅ Zoom 9 pour navigation détaillée")
    print(f"✅ Satellite + Labels offline")

if __name__ == "__main__":
    main()
