#!/usr/bin/env python3
"""
Analyse simple du fichier MNT Maroc
"""

import os
from pathlib import Path

def analyser_fichier():
    """Analyser le fichier MNT"""
    
    # Chemin vers le fichier
    mnt_file = Path(r"backend\app\mnt maroc\mnt_maroc.tif")
    
    print("🗺️ ANALYSE FICHIER MNT MAROC")
    print("=" * 40)
    
    if mnt_file.exists():
        size_gb = mnt_file.stat().st_size / (1024**3)
        print(f"✅ Fichier trouvé: {mnt_file}")
        print(f"💾 Taille: {size_gb:.2f} GB")
        
        # Essayer d'analyser avec rasterio
        try:
            import rasterio
            print("📊 Ouverture avec rasterio...")
            
            with rasterio.open(mnt_file) as dataset:
                print(f"   📏 Dimensions: {dataset.width} x {dataset.height}")
                print(f"   🗺️  CRS: {dataset.crs}")
                print(f"   📐 Résolution: {dataset.res}")
                print(f"   🌍 Bounds: {dataset.bounds}")
                print(f"   📊 Type: {dataset.dtypes[0]}")
                
                # Calculer recommandations
                total_pixels = dataset.width * dataset.height
                print(f"   🔢 Total pixels: {total_pixels:,}")
                
                # Recommandation de découpage
                target_pixels_per_tile = 4096 * 4096  # 16M pixels par tuile
                nb_tiles = total_pixels / target_pixels_per_tile
                print(f"   🔪 Tuiles recommandées: ~{nb_tiles:.0f}")
                
        except Exception as e:
            print(f"❌ Erreur rasterio: {e}")
            
    else:
        print(f"❌ Fichier non trouvé: {mnt_file}")
        
        # Lister le contenu du dossier
        parent_dir = mnt_file.parent
        if parent_dir.exists():
            print(f"📁 Contenu de {parent_dir}:")
            for item in parent_dir.iterdir():
                print(f"   - {item.name}")

if __name__ == "__main__":
    analyser_fichier()
