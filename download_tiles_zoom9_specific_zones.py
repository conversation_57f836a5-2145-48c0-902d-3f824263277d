#!/usr/bin/env python3
"""
Téléchargement de tuiles zoom 9 pour zones spécifiques :
- À gauche d'El Bioudh et Ain Sefra et Adrar
- En bas de Tamamt
"""

import os
import requests
import time
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_tile(url, filepath, max_retries=3):
    """Télécharge une tuile avec gestion d'erreurs"""
    if os.path.exists(filepath):
        return False  # Déjà téléchargée
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
            elif response.status_code == 404:
                return False  # Tuile n'existe pas
            else:
                print(f"⚠️  Erreur {response.status_code} pour {url}")
                
        except Exception as e:
            print(f"❌ Erreur téléchargement (tentative {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Backoff exponentiel
    
    return False

def download_zone_tiles(zone_name, bounds, zoom, tile_type="satellite"):
    """Télécharge les tuiles pour une zone spécifique"""
    print(f"\n🗺️  TÉLÉCHARGEMENT ZONE: {zone_name}")
    print(f"📍 Limites: Nord {bounds['north']}°, Sud {bounds['south']}°")
    print(f"📍 Limites: Ouest {bounds['west']}°, Est {bounds['east']}°")
    print(f"🔍 Zoom: {zoom}")
    
    # Calcul des tuiles (correction des coordonnées)
    x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
    x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)

    # Correction si les coordonnées sont inversées
    if x_min > x_max:
        x_min, x_max = x_max, x_min
    if y_min > y_max:
        y_min, y_max = y_max, y_min

    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📊 Tuiles à traiter: {total_tiles}")
    print(f"📈 Tuiles: X={x_min}-{x_max}, Y={y_min}-{y_max}")
    
    # Configuration selon le type
    if tile_type == "satellite":
        base_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile"
        folder = "tiles/esri_satellite_morocco"
    else:  # labels
        base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
        folder = "tiles/carto_labels"
    
    downloaded = 0
    existing = 0
    
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            if tile_type == "satellite":
                url = f"{base_url}/{zoom}/{y}/{x}"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            else:
                url = f"{base_url}/{zoom}/{x}/{y}.png"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
            
            if download_tile(url, filepath):
                downloaded += 1
                if downloaded % 10 == 0:
                    print(f"📥 Téléchargées: {downloaded}/{total_tiles}")
            else:
                existing += 1
            
            time.sleep(0.1)  # Délai pour éviter la surcharge
    
    print(f"✅ Zone {zone_name} terminée:")
    print(f"🔽 Nouvelles: {downloaded}")
    print(f"✅ Existantes: {existing}")
    return downloaded, existing

def main():
    print("🗺️  TÉLÉCHARGEMENT TUILES ZOOM 9 - ZONES SPÉCIFIQUES")
    print("🎯 Objectif: Étendre la couverture vers zones demandées")
    
    # ZONES SPÉCIFIQUES À TÉLÉCHARGER
    zones = {
        "El_Bioudh_Ouest": {
            "name": "À gauche d'El Bioudh",
            "bounds": {
                "north": 35.0,   # Nord d'El Bioudh
                "south": 33.5,   # Sud d'El Bioudh
                "west": -0.5,    # À gauche d'El Bioudh (ouest)
                "east": 1.5      # Vers El Bioudh
            }
        },
        "Ain_Sefra_Ouest": {
            "name": "À gauche d'Ain Sefra",
            "bounds": {
                "north": 33.0,   # Nord d'Ain Sefra
                "south": 31.5,   # Sud d'Ain Sefra
                "west": -2.0,    # À gauche d'Ain Sefra (ouest)
                "east": 0.0      # Vers Ain Sefra
            }
        },
        "Adrar_Ouest": {
            "name": "À gauche d'Adrar",
            "bounds": {
                "north": 28.5,   # Nord d'Adrar
                "south": 27.0,   # Sud d'Adrar
                "west": -1.5,    # À gauche d'Adrar (ouest)
                "east": 0.0      # Vers Adrar
            }
        },
        "Tamamt_Sud": {
            "name": "En bas de Tamamt",
            "bounds": {
                "north": 26.0,   # Tamamt
                "south": 24.0,   # En bas de Tamamt (sud)
                "west": -2.0,    # Ouest de Tamamt
                "east": 0.0      # Est de Tamamt
            }
        }
    }
    
    total_downloaded = 0
    total_existing = 0
    
    # Télécharger pour chaque zone
    for zone_id, zone_config in zones.items():
        print(f"\n{'='*60}")
        
        # Satellite
        print(f"📡 SATELLITE - {zone_config['name']}")
        sat_down, sat_exist = download_zone_tiles(
            zone_config['name'], 
            zone_config['bounds'], 
            9, 
            "satellite"
        )
        
        # Labels
        print(f"🏷️  LABELS - {zone_config['name']}")
        lab_down, lab_exist = download_zone_tiles(
            zone_config['name'], 
            zone_config['bounds'], 
            9, 
            "labels"
        )
        
        total_downloaded += sat_down + lab_down
        total_existing += sat_exist + lab_exist
    
    print(f"\n{'='*60}")
    print("🎉 TÉLÉCHARGEMENT ZONES SPÉCIFIQUES TERMINÉ!")
    print(f"🔽 Total téléchargé: {total_downloaded}")
    print(f"✅ Total existant: {total_existing}")
    print(f"📁 Dossiers: tiles/esri_satellite_morocco/ et tiles/carto_labels/")
    
    print(f"\n🌍 RÉSULTAT:")
    print(f"✅ Extension vers El Bioudh, Ain Sefra, Adrar")
    print(f"✅ Extension vers le sud de Tamamt")
    print(f"✅ Zoom 9 pour navigation détaillée")
    print(f"✅ Satellite + Labels offline")

if __name__ == "__main__":
    main()
