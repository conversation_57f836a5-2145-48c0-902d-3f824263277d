#!/usr/bin/env python3
"""
Diagnostic et correction des tuiles manquantes zoom 6
Région étendue : Ouargla → Misrata → Niger → Sabha + extensions ouest
"""

import os
import requests
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def calculate_point_at_distance(lat, lon, bearing_deg, distance_km):
    """Calcule un point à une distance donnée dans une direction"""
    R = 6371  # Rayon de la Terre en km
    bearing = math.radians(bearing_deg)
    lat1 = math.radians(lat)
    lon1 = math.radians(lon)
    
    lat2 = math.asin(math.sin(lat1) * math.cos(distance_km/R) +
                     math.cos(lat1) * math.sin(distance_km/R) * math.cos(bearing))
    
    lon2 = lon1 + math.atan2(math.sin(bearing) * math.sin(distance_km/R) * math.cos(lat1),
                             math.cos(distance_km/R) - math.sin(lat1) * math.sin(lat2))
    
    return math.degrees(lat2), math.degrees(lon2)

def check_tile_exists(tile_info):
    """Vérifie si une tuile existe localement"""
    x, y, z, _, base_dir, source_name = tile_info
    tile_path = os.path.join(base_dir, source_name, str(z), str(x), f"{y}.png")
    return os.path.exists(tile_path), tile_path

def download_tile(tile_info):
    """Télécharge une tuile manquante"""
    x, y, z, source_url, base_dir, source_name = tile_info
    
    tile_dir = os.path.join(base_dir, source_name, str(z), str(x))
    tile_path = os.path.join(tile_dir, f"{y}.png")
    
    # Vérifier si existe déjà
    if os.path.exists(tile_path):
        return f"Existe: {z}/{x}/{y}"
    
    url = source_url.format(x=x, y=y, z=z)
    
    try:
        os.makedirs(tile_dir, exist_ok=True)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        with open(tile_path, 'wb') as f:
            f.write(response.content)
        
        return f"✓ {z}/{x}/{y}"
        
    except Exception as e:
        return f"✗ {z}/{x}/{y}: {str(e)}"

def main():
    print("=" * 80)
    print("  DIAGNOSTIC ET CORRECTION TUILES ZOOM 6 - RÉGION ÉTENDUE")
    print("=" * 80)
    
    base_dir = os.path.join(os.path.dirname(__file__), "..", "tiles")
    source_name = "esri_satellite_morocco"
    source_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    
    # Points de référence
    cities = {
        'Ouargla': (31.9333, 5.3167),      # Algérie
        'Misrata': (32.3744, 15.0919),     # Libye  
        'Sabha': (27.0377, 14.4283),       # Libye Sud
        'Funchal': (32.6669, -16.9241),    # Madère
        'Santa_Cruz_Tenerife': (28.4636, -16.2518),  # Canaries
        'Agadez': (16.9716, 7.9911),       # Niger
    }
    
    print("🎯 RÉGION ÉTENDUE ZOOM 6:")
    print("   📍 Nord: Ouargla (Algérie)")
    print("   📍 Est: Misrata (Libye)")
    print("   📍 Sud-Est: Sabha (Libye)")
    print("   📍 Sud: Niger (Agadez)")
    print("   📍 Ouest: 489km à gauche de Funchal")
    print("   📍 Ouest: 516km à gauche de Santa Cruz de Tenerife")
    
    # Calcul des limites étendues
    funchal_lat, funchal_lon = cities['Funchal']
    west_funchal_lat, west_funchal_lon = calculate_point_at_distance(
        funchal_lat, funchal_lon, 270, 489
    )
    
    santa_cruz_lat, santa_cruz_lon = cities['Santa_Cruz_Tenerife']
    west_santa_lat, west_santa_lon = calculate_point_at_distance(
        santa_cruz_lat, santa_cruz_lon, 270, 516
    )
    
    # Prendre le point le plus à l'ouest
    west_lon = min(west_funchal_lon, west_santa_lon)
    
    # Limites finales
    bounds = {
        'north': cities['Ouargla'][0],      # 31.93°N
        'south': cities['Agadez'][0],       # 16.97°N  
        'east': cities['Misrata'][1],       # 15.09°E
        'west': west_lon                    # ~-22°W
    }
    
    print(f"\n📊 LIMITES CALCULÉES:")
    print(f"   Nord:  {bounds['north']:7.3f}°N (Ouargla)")
    print(f"   Sud:   {bounds['south']:7.3f}°N (Niger)")
    print(f"   Est:   {bounds['east']:7.3f}°E (Misrata)")
    print(f"   Ouest: {bounds['west']:7.3f}°W (489km de Funchal)")
    
    # Calcul des tuiles pour zoom 6
    zoom = 6
    x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
    x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
    
    # Corriger l'ordre des Y
    if y_min > y_max:
        y_min, y_max = y_max, y_min
    
    # Ajuster les limites
    x_min = max(0, x_min)
    x_max = min(2**zoom - 1, x_max)
    y_min = max(0, y_min)
    y_max = min(2**zoom - 1, y_max)
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    
    print(f"\n📈 ANALYSE TUILES ZOOM {zoom}:")
    print(f"   X: {x_min} → {x_max} ({x_max - x_min + 1} tuiles)")
    print(f"   Y: {y_min} → {y_max} ({y_max - y_min + 1} tuiles)")
    print(f"   Total: {total_tiles} tuiles")
    
    # Préparer la liste des tuiles
    all_tiles = []
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            all_tiles.append((x, y, zoom, source_url, base_dir, source_name))
    
    print(f"\n🔍 DIAGNOSTIC DES TUILES EXISTANTES...")
    
    # Vérifier quelles tuiles existent
    existing_tiles = []
    missing_tiles = []
    
    for tile in tqdm(all_tiles, desc="Vérification"):
        exists, path = check_tile_exists(tile)
        if exists:
            existing_tiles.append(tile)
        else:
            missing_tiles.append(tile)
    
    print(f"\n📊 RÉSULTAT DIAGNOSTIC:")
    print(f"   ✅ Tuiles existantes: {len(existing_tiles)}")
    print(f"   ❌ Tuiles manquantes: {len(missing_tiles)}")
    print(f"   📈 Couverture: {len(existing_tiles)/len(all_tiles)*100:.1f}%")
    
    if len(missing_tiles) == 0:
        print("\n🎉 Toutes les tuiles sont présentes ! Pas de carreaux cassés.")
        return
    
    print(f"\n🚨 TUILES MANQUANTES DÉTECTÉES:")
    for i, (x, y, z, _, _, _) in enumerate(missing_tiles[:10]):  # Afficher les 10 premières
        print(f"   - {z}/{x}/{y}")
        if i == 9 and len(missing_tiles) > 10:
            print(f"   ... et {len(missing_tiles) - 10} autres")
    
    # Proposer le téléchargement
    if len(missing_tiles) > 0:
        print(f"\n💾 TÉLÉCHARGEMENT DES TUILES MANQUANTES")
        response = input(f"Télécharger {len(missing_tiles)} tuiles manquantes? (o/N): ")
        if response.lower() == 'o':
            print(f"\n🚀 TÉLÉCHARGEMENT EN COURS...")
            
            start_time = time.time()
            downloaded = 0
            errors = 0
            
            with ThreadPoolExecutor(max_workers=4) as executor:
                with tqdm(total=len(missing_tiles), desc="Téléchargement") as pbar:
                    future_to_tile = {executor.submit(download_tile, tile): tile for tile in missing_tiles}
                    
                    for future in as_completed(future_to_tile):
                        result = future.result()
                        pbar.update(1)
                        
                        if result.startswith("✓"):
                            downloaded += 1
                        else:
                            errors += 1
                        
                        pbar.set_description(f"✓{downloaded} ✗{errors}")
                        time.sleep(0.1)
            
            elapsed_time = time.time() - start_time
            
            print(f"\n✅ TÉLÉCHARGEMENT TERMINÉ!")
            print(f"   Temps: {elapsed_time:.1f}s")
            print(f"   Téléchargées: {downloaded}")
            print(f"   Erreurs: {errors}")
            
            if downloaded > 0:
                print(f"\n🎯 PROBLÈME RÉSOLU!")
                print(f"   Les carreaux cassés devraient maintenant être corrigés.")
                print(f"   Rafraîchissez votre navigateur pour voir les changements.")

if __name__ == "__main__":
    main()
