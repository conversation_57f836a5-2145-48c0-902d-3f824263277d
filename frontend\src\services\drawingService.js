// Service pour la gestion des dessins tactiques avec PostgreSQL
class DrawingService {
  constructor() {
    this.baseUrl = '/api/drawings';
  }

  // Sauvegarder un dessin
  async saveDrawing(drawing) {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: drawing.id || this.generateId(),
          type: drawing.type, // 'circle', 'polygon', 'line', 'marker', etc.
          coordinates: drawing.coordinates,
          properties: {
            color: drawing.color,
            comment: drawing.comment,
            radius: drawing.radius, // pour les cercles
            area: drawing.area, // pour les polygones
            distance: drawing.distance, // pour les lignes
            icon: drawing.icon, // pour les marqueurs
            style: drawing.style,
            timestamp: new Date().toISOString(),
            user: 'tactical_user' // À remplacer par l'utilisateur connecté
          },
          metadata: {
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            session_id: this.getSessionId()
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur sauvegarde: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Dessin sauvegardé:', result);
      return result;
    } catch (error) {
      console.error('❌ Erreur sauvegarde dessin:', error);
      throw error;
    }
  }

  // Charger tous les dessins
  async loadDrawings() {
    try {
      const response = await fetch(`${this.baseUrl}`);
      
      if (!response.ok) {
        throw new Error(`Erreur chargement: ${response.status}`);
      }

      const drawings = await response.json();
      console.log('✅ Dessins chargés:', drawings.length);
      return drawings;
    } catch (error) {
      console.error('❌ Erreur chargement dessins:', error);
      return [];
    }
  }

  // Mettre à jour un dessin
  async updateDrawing(id, updates) {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          metadata: {
            ...updates.metadata,
            updated_at: new Date().toISOString()
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur mise à jour: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Dessin mis à jour:', result);
      return result;
    } catch (error) {
      console.error('❌ Erreur mise à jour dessin:', error);
      throw error;
    }
  }

  // Supprimer un dessin
  async deleteDrawing(id) {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Erreur suppression: ${response.status}`);
      }

      console.log('✅ Dessin supprimé:', id);
      return true;
    } catch (error) {
      console.error('❌ Erreur suppression dessin:', error);
      throw error;
    }
  }

  // Utilitaires
  generateId() {
    return `drawing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getSessionId() {
    let sessionId = localStorage.getItem('tactical_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('tactical_session_id', sessionId);
    }
    return sessionId;
  }

  // Convertir un objet Leaflet en format de sauvegarde
  leafletToDrawing(leafletObject, type) {
    const drawing = {
      type: type,
      coordinates: this.extractCoordinates(leafletObject, type),
      color: leafletObject.options?.color || '#ff0000',
      comment: leafletObject.getPopup()?.getContent() || '',
      style: leafletObject.options || {}
    };

    // Propriétés spécifiques selon le type
    switch (type) {
      case 'circle':
        drawing.radius = leafletObject.getRadius();
        drawing.center = [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];
        drawing.area = Math.PI * Math.pow(leafletObject.getRadius(), 2) / 1000000; // km²
        break;
      
      case 'polygon':
        drawing.area = this.calculatePolygonArea(leafletObject.getLatLngs()[0]);
        break;
      
      case 'polyline':
        drawing.distance = this.calculateDistance(leafletObject.getLatLngs());
        break;
      
      case 'marker':
        drawing.icon = leafletObject.options.icon?.options?.iconUrl || '📍';
        break;
    }

    return drawing;
  }

  // Extraire les coordonnées selon le type
  extractCoordinates(leafletObject, type) {
    switch (type) {
      case 'circle':
        return [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];
      
      case 'marker':
        return [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];
      
      case 'polygon':
        return leafletObject.getLatLngs()[0].map(latlng => [latlng.lat, latlng.lng]);
      
      case 'polyline':
        return leafletObject.getLatLngs().map(latlng => [latlng.lat, latlng.lng]);
      
      default:
        return [];
    }
  }

  // Calculer la distance d'une ligne
  calculateDistance(latlngs) {
    let totalDistance = 0;
    for (let i = 0; i < latlngs.length - 1; i++) {
      totalDistance += latlngs[i].distanceTo(latlngs[i + 1]);
    }
    return totalDistance / 1000; // km
  }

  // Calculer l'aire d'un polygone
  calculatePolygonArea(latlngs) {
    if (latlngs.length < 3) return 0;
    
    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres
    
    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // km²
  }

  // Créer un objet Leaflet depuis les données sauvegardées
  drawingToLeaflet(drawing, L) {
    let leafletObject;

    switch (drawing.type) {
      case 'circle':
        leafletObject = L.circle(drawing.center, {
          radius: drawing.radius,
          ...drawing.style
        });
        break;
      
      case 'polygon':
        leafletObject = L.polygon(drawing.coordinates, drawing.style);
        break;
      
      case 'polyline':
        leafletObject = L.polyline(drawing.coordinates, drawing.style);
        break;
      
      case 'marker':
        leafletObject = L.marker(drawing.coordinates);
        if (drawing.icon) {
          // Configurer l'icône personnalisée
        }
        break;
      
      default:
        return null;
    }

    // Ajouter le popup avec commentaire
    if (drawing.comment) {
      leafletObject.bindPopup(this.formatPopupContent(drawing));
    }

    // Ajouter l'ID pour la gestion
    leafletObject._drawingId = drawing.id;

    return leafletObject;
  }

  // Formater le contenu du popup
  formatPopupContent(drawing) {
    let content = `<div class="tactical-popup">`;
    
    if (drawing.comment) {
      content += `<div class="comment"><strong>Commentaire:</strong> ${drawing.comment}</div>`;
    }
    
    if (drawing.distance) {
      content += `<div class="distance"><strong>Distance:</strong> ${drawing.distance.toFixed(2)} km</div>`;
    }
    
    if (drawing.radius) {
      content += `<div class="radius"><strong>Rayon:</strong> ${(drawing.radius/1000).toFixed(2)} km</div>`;
    }
    
    if (drawing.area) {
      content += `<div class="area"><strong>Surface:</strong> ${drawing.area.toFixed(2)} km²</div>`;
    }
    
    content += `<div class="timestamp"><small>Créé: ${new Date(drawing.properties.timestamp).toLocaleString()}</small></div>`;
    content += `</div>`;
    
    return content;
  }
}

// Instance singleton
const drawingService = new DrawingService();
export default drawingService;
