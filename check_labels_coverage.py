#!/usr/bin/env python3
"""
Script pour vérifier la couverture géographique des labels téléchargés
et s'assurer que les villes du sud sont incluses
"""

import math

def tile_to_lat_lon(x, y, z):
    """Convertir coordonnées de tuile en latitude/longitude"""
    n = 2.0 ** z
    lon_deg = x / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
    lat_deg = math.degrees(lat_rad)
    return lat_deg, lon_deg

def lat_lon_to_tile(lat, lon, z):
    """Convertir latitude/longitude en coordonnées de tuile"""
    lat_rad = math.radians(lat)
    n = 2.0 ** z
    x = int((lon + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def check_coverage():
    """Vérifier la couverture des zones importantes"""
    
    # Villes importantes à vérifier
    cities = {
        # Maroc Nord
        "Rabat": (34.0209, -6.8416),
        "Casablanca": (33.5731, -7.5898),
        "<PERSON>er": (35.7595, -5.8340),
        
        # Maroc Centre
        "Marrakech": (31.6295, -7.9811),
        "F<PERSON>": (34.0181, -5.0078),
        
        # Zone Sud Maroc
        "Laayoune": (27.1253, -13.1625),
        "Dakhla": (23.6848, -15.9580),
        "Smara": (26.7386, -11.6719),
        
        # Zone Sud Extrême (Sahara/Mauritanie)
        "Tichla": (24.0833, -15.1667),
        "Birhlou": (25.9167, -12.3333),
        "Tifaritit": (26.1500, -10.7000),
        "Tindouf": (27.6667, -8.1333),
        "Nouadhibou": (20.9316, -17.0351),
    }
    
    print("🔍 VÉRIFICATION COUVERTURE LABELS")
    print("=" * 50)
    
    for zoom in range(6, 14):
        print(f"\n📊 ZOOM {zoom}")
        
        # Limites actuelles du script
        if zoom <= 6:
            x_min, x_max = 30, 35
            y_min, y_max = 18, 25
        elif zoom <= 8:
            x_min, x_max = 60, 70
            y_min, y_max = 36, 50
        elif zoom <= 10:
            x_min, x_max = 120, 140
            y_min, y_max = 72, 100
        elif zoom <= 12:
            x_min, x_max = 240, 280
            y_min, y_max = 144, 200
        else:
            x_min, x_max = 480, 560
            y_min, y_max = 288, 400
        
        # Calculer les limites géographiques
        lat_max, lon_min = tile_to_lat_lon(x_min, y_min, zoom)
        lat_min, lon_max = tile_to_lat_lon(x_max, y_max, zoom)
        
        print(f"📍 Limites géographiques:")
        print(f"   Nord: {lat_max:.2f}°, Sud: {lat_min:.2f}°")
        print(f"   Ouest: {lon_min:.2f}°, Est: {lon_max:.2f}°")
        
        # Vérifier chaque ville
        covered_cities = []
        missing_cities = []
        
        for city, (lat, lon) in cities.items():
            x, y = lat_lon_to_tile(lat, lon, zoom)
            
            if x_min <= x <= x_max and y_min <= y <= y_max:
                covered_cities.append(city)
            else:
                missing_cities.append(f"{city} (X={x}, Y={y})")
        
        print(f"✅ Villes couvertes ({len(covered_cities)}): {', '.join(covered_cities)}")
        if missing_cities:
            print(f"❌ Villes MANQUANTES ({len(missing_cities)}): {', '.join(missing_cities)}")
        else:
            print("🎉 Toutes les villes sont couvertes !")
    
    print("\n" + "=" * 50)
    print("🎯 RECOMMANDATIONS:")
    print("Si des villes du sud manquent, ajustez les limites dans download_labels_all_zooms.py")

if __name__ == "__main__":
    check_coverage()
