#!/usr/bin/env python3
"""
Script de découpage optimisé pour le MNT Maroc
Découpe le fichier global en tuiles performantes pour C2-EW
"""

import os
import sys
from pathlib import Path
import math
import json

# Configuration environnement pour éviter conflits PROJ
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

# Nettoyer PATH des références PostgreSQL
current_path = os.environ.get('PATH', '')
clean_path = []
for part in current_path.split(os.pathsep):
    if 'postgresql' not in part.lower():
        clean_path.append(part)
os.environ['PATH'] = os.pathsep.join(clean_path)

try:
    import rasterio
    from rasterio.windows import Window
    from rasterio.warp import calculate_default_transform, reproject, Resampling
    from rasterio.crs import CRS
    import numpy as np
    from tqdm import tqdm
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    print("💡 Installez les dépendances: pip install rasterio tqdm")
    sys.exit(1)

class DecoupeurMNTMaroc:
    """Découpeur optimisé pour le MNT Maroc"""
    
    def __init__(self):
        # Chemins
        self.base_dir = Path(__file__).parent.parent
        self.source_file = self.base_dir / "backend" / "app" / "mnt maroc" / "mnt_maroc.tif"
        self.output_dir = self.base_dir / "backend" / "app" / "MNT"
        
        # Créer le dossier de sortie
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistiques
        self.stats = {
            'tuiles_creees': 0,
            'taille_totale_mb': 0,
            'erreurs': 0
        }
        
        print(f"📁 Fichier source: {self.source_file}")
        print(f"📁 Dossier sortie: {self.output_dir}")
    
    def analyser_source(self):
        """Analyser le fichier source"""
        if not self.source_file.exists():
            print(f"❌ Fichier source non trouvé: {self.source_file}")
            return False
        
        try:
            with rasterio.open(self.source_file) as src:
                self.src_info = {
                    'width': src.width,
                    'height': src.height,
                    'crs': src.crs,
                    'transform': src.transform,
                    'bounds': src.bounds,
                    'dtype': src.dtypes[0],
                    'nodata': src.nodata,
                    'resolution': src.res
                }
                
                size_gb = self.source_file.stat().st_size / (1024**3)
                
                print(f"📊 ANALYSE DU FICHIER SOURCE:")
                print(f"   💾 Taille: {size_gb:.2f} GB")
                print(f"   📏 Dimensions: {src.width:,} x {src.height:,} pixels")
                print(f"   🗺️  CRS: {src.crs}")
                print(f"   📐 Résolution: {src.res[0]:.8f}° x {src.res[1]:.8f}°")
                print(f"   🌍 Bounds: {src.bounds}")
                
                return True
                
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse: {e}")
            return False
    
    def calculer_strategie_decoupage(self):
        """Calculer la stratégie de découpage optimale"""
        
        # Paramètres optimaux pour C2-EW
        TARGET_TILE_SIZE_MB = 25  # MB par tuile
        MAX_TILE_SIZE_PIXELS = 4096  # pixels max par côté
        
        # Calculer la taille optimale en pixels
        bytes_per_pixel = np.dtype(self.src_info['dtype']).itemsize
        target_pixels = (TARGET_TILE_SIZE_MB * 1024 * 1024) / bytes_per_pixel
        tile_size_pixels = min(int(math.sqrt(target_pixels)), MAX_TILE_SIZE_PIXELS)
        
        # Calculer le nombre de tuiles
        nb_tiles_x = math.ceil(self.src_info['width'] / tile_size_pixels)
        nb_tiles_y = math.ceil(self.src_info['height'] / tile_size_pixels)
        
        self.strategie = {
            'tile_size_pixels': tile_size_pixels,
            'nb_tiles_x': nb_tiles_x,
            'nb_tiles_y': nb_tiles_y,
            'total_tiles': nb_tiles_x * nb_tiles_y,
            'estimated_size_mb': TARGET_TILE_SIZE_MB
        }
        
        print(f"🎯 STRATÉGIE DE DÉCOUPAGE:")
        print(f"   🔢 Taille tuile: {tile_size_pixels} x {tile_size_pixels} pixels")
        print(f"   📊 Nombre de tuiles: {nb_tiles_x} x {nb_tiles_y} = {self.strategie['total_tiles']}")
        print(f"   💾 Taille estimée par tuile: ~{TARGET_TILE_SIZE_MB} MB")
        print(f"   💾 Taille totale estimée: ~{self.strategie['total_tiles'] * TARGET_TILE_SIZE_MB / 1024:.1f} GB")
        
        return True
    
    def creer_tuile(self, src_dataset, tile_x, tile_y):
        """Créer une tuile individuelle"""
        try:
            # Calculer la fenêtre
            tile_size = self.strategie['tile_size_pixels']
            
            col_start = tile_x * tile_size
            row_start = tile_y * tile_size
            
            # Ajuster pour les bords
            col_end = min(col_start + tile_size, self.src_info['width'])
            row_end = min(row_start + tile_size, self.src_info['height'])
            
            width = col_end - col_start
            height = row_end - row_start
            
            # Ignorer les tuiles trop petites
            if width < 100 or height < 100:
                return False
            
            # Créer la fenêtre
            window = Window(col_start, row_start, width, height)
            
            # Lire les données
            data = src_dataset.read(1, window=window)
            
            # Vérifier qu'il y a des données valides
            if np.all(data == self.src_info['nodata']):
                return False
            
            # Calculer la transformation pour cette tuile
            transform = rasterio.windows.transform(window, src_dataset.transform)
            
            # Nom de fichier basé sur la position
            filename = f"tile_{tile_y:03d}_{tile_x:03d}.tif"
            output_path = self.output_dir / filename
            
            # Profil pour la tuile (optimisé COG)
            profile = {
                'driver': 'GTiff',
                'height': height,
                'width': width,
                'count': 1,
                'dtype': self.src_info['dtype'],
                'crs': self.src_info['crs'],
                'transform': transform,
                'nodata': self.src_info['nodata'],
                'compress': 'LZW',
                'tiled': True,
                'blockxsize': 256,
                'blockysize': 256,
                'BIGTIFF': 'IF_SAFER'
            }
            
            # Écrire la tuile
            with rasterio.open(output_path, 'w', **profile) as dst:
                dst.write(data, 1)
                
                # Ajouter des pyramides pour l'optimisation
                if width > 512 and height > 512:
                    factors = [2, 4, 8]
                    dst.build_overviews(factors, Resampling.average)
                    dst.update_tags(ns='rio_overview', resampling='average')
            
            # Mettre à jour les statistiques
            size_mb = output_path.stat().st_size / (1024 * 1024)
            self.stats['tuiles_creees'] += 1
            self.stats['taille_totale_mb'] += size_mb
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur création tuile {tile_y}_{tile_x}: {e}")
            self.stats['erreurs'] += 1
            return False
    
    def decouper(self):
        """Exécuter le découpage"""
        print(f"\n🔪 DÉBUT DU DÉCOUPAGE...")
        
        try:
            with rasterio.open(self.source_file) as src:
                total_tiles = self.strategie['total_tiles']
                
                # Barre de progression
                with tqdm(total=total_tiles, desc="Création tuiles", unit="tuile") as pbar:
                    
                    for tile_y in range(self.strategie['nb_tiles_y']):
                        for tile_x in range(self.strategie['nb_tiles_x']):
                            
                            success = self.creer_tuile(src, tile_x, tile_y)
                            
                            if success:
                                pbar.set_postfix({
                                    'Créées': self.stats['tuiles_creees'],
                                    'Erreurs': self.stats['erreurs'],
                                    'Taille': f"{self.stats['taille_totale_mb']:.0f}MB"
                                })
                            
                            pbar.update(1)
                
                return True
                
        except Exception as e:
            print(f"❌ Erreur lors du découpage: {e}")
            return False
    
    def sauvegarder_metadata(self):
        """Sauvegarder les métadonnées"""
        metadata = {
            'source_file': str(self.source_file),
            'output_dir': str(self.output_dir),
            'strategie': self.strategie,
            'stats': self.stats,
            'src_info': {
                'bounds': self.src_info['bounds'],
                'crs': str(self.src_info['crs']),
                'resolution': self.src_info['resolution']
            }
        }
        
        metadata_file = self.output_dir / "metadata_decoupage.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📋 Métadonnées sauvegardées: {metadata_file}")
    
    def afficher_resume(self):
        """Afficher le résumé final"""
        print(f"\n📊 RÉSUMÉ DU DÉCOUPAGE:")
        print(f"   ✅ Tuiles créées: {self.stats['tuiles_creees']}")
        print(f"   ❌ Erreurs: {self.stats['erreurs']}")
        print(f"   💾 Taille totale: {self.stats['taille_totale_mb']:.1f} MB")
        print(f"   📁 Dossier: {self.output_dir}")
        
        if self.stats['tuiles_creees'] > 0:
            avg_size = self.stats['taille_totale_mb'] / self.stats['tuiles_creees']
            print(f"   📊 Taille moyenne par tuile: {avg_size:.1f} MB")

def main():
    print("🗺️ DÉCOUPAGE MNT MAROC POUR C2-EW")
    print("=" * 50)
    
    # Créer le découpeur
    decoupeur = DecoupeurMNTMaroc()
    
    # Analyser le fichier source
    if not decoupeur.analyser_source():
        sys.exit(1)
    
    # Calculer la stratégie
    if not decoupeur.calculer_strategie_decoupage():
        sys.exit(1)
    
    # Demander confirmation
    print(f"\n❓ Continuer avec le découpage de {decoupeur.strategie['total_tiles']} tuiles ? (y/N)")
    response = input().strip().lower()
    
    if response != 'y':
        print("❌ Découpage annulé")
        sys.exit(0)
    
    # Exécuter le découpage
    if decoupeur.decouper():
        decoupeur.sauvegarder_metadata()
        decoupeur.afficher_resume()
        
        print(f"\n✅ DÉCOUPAGE TERMINÉ AVEC SUCCÈS!")
        print(f"🎯 Prêt pour l'indexation spatiale:")
        print(f"   python scripts/init_mnt_index.py")
        
    else:
        print(f"\n❌ ÉCHEC DU DÉCOUPAGE")
        sys.exit(1)

if __name__ == "__main__":
    main()
