#!/usr/bin/env python3
"""
Script pour télécharger les tuiles de labels (noms de villes) offline
pour les zooms 4-5 dans la zone globale
"""

import os
import requests
import time
import math
from pathlib import Path

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile de labels"""
    tile_url = base_url.format(z=z, x=x, y=y, s='a')
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # Créer les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        print(f"✅ Existe: {z}/{x}/{y}")
        return True
    
    try:
        response = session.get(tile_url, timeout=15)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"🔽 Labels: {z}/{x}/{y}")
            return True
        else:
            print(f"❌ Erreur {response.status_code}: {z}/{x}/{y}")
            return False
    except Exception as e:
        print(f"❌ Exception: {z}/{x}/{y} - {e}")
        return False

def main():
    # Configuration pour labels
    BASE_URL = "https://{s}.basemaps.cartocdn.com/light_only_labels/{z}/{x}/{y}.png"
    OUTPUT_DIR = Path("tiles/carto_labels")
    
    print(f"🏷️ TÉLÉCHARGEMENT LABELS OFFLINE")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Télécharger pour zooms 4 et 5
    for zoom in [4, 5]:
        print(f"\n📊 ZOOM {zoom}")
        
        if zoom == 4:
            # Zoom 4 : Zone plus restreinte
            X_MIN, X_MAX = 3, 12
            Y_MIN, Y_MAX = 4, 10
        else:  # zoom == 5
            # Zoom 5 : Zone étendue comme configuré
            X_MIN, X_MAX = 6, 25
            Y_MIN, Y_MAX = 8, 18
        
        total_tiles = (X_MAX - X_MIN + 1) * (Y_MAX - Y_MIN + 1)
        print(f"📈 Tuiles à télécharger: {total_tiles}")
        
        downloaded = 0
        failed = 0
        existing = 0
        
        for x in range(X_MIN, X_MAX + 1):
            for y in range(Y_MIN, Y_MAX + 1):
                tile_path = OUTPUT_DIR / str(zoom) / str(x) / f"{y}.png"
                
                if tile_path.exists():
                    existing += 1
                    continue
                
                success = download_tile(x, y, zoom, BASE_URL, OUTPUT_DIR, session)
                if success:
                    downloaded += 1
                else:
                    failed += 1
                
                # Pause pour éviter la surcharge
                time.sleep(0.1)
                
                # Afficher le progrès
                if (downloaded + failed) % 10 == 0:
                    total_processed = downloaded + failed + existing
                    progress = (total_processed / total_tiles) * 100
                    print(f"📊 Zoom {zoom}: {progress:.1f}% ({downloaded} nouveaux, {existing} existants)")
        
        print(f"✅ Zoom {zoom} terminé: {downloaded} nouveaux, {existing} existants, {failed} échecs")
    
    print(f"\n🎉 LABELS OFFLINE TERMINÉ!")
    print(f"📁 Dossier: tiles/carto_labels/")
    print(f"🔧 Maintenant, ajoutez la couche labels offline dans MoroccoMap.jsx")

if __name__ == "__main__":
    main()
