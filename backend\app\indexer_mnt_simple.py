#!/usr/bin/env python3
"""
Indexeur MNT simplifié pour fichiers pré-découpés 25MB
Optimisé pour C2-EW avec fichiers déjà optimisés
"""

import os
import pickle
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import structlog

# Configuration environnement PROJ simplifié
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

try:
    import rasterio
    from rasterio.crs import CRS
    from rtree import index
except ImportError as e:
    print(f"❌ Dépendances manquantes: {e}")
    print("💡 Installez: pip install rasterio rtree")
    exit(1)

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger()

class IndexeurMNTSimple:
    """Indexeur optimisé pour fichiers MNT pré-découpés"""
    
    def __init__(self, mnt_directory: str = None, index_file: str = None):
        if mnt_directory is None:
            current_dir = Path(__file__).parent
            mnt_directory = current_dir / "MNT"
        if index_file is None:
            current_dir = Path(__file__).parent
            index_file = current_dir / "mnt_index.pkl"
            
        self.mnt_directory = Path(mnt_directory)
        self.index_file = Path(index_file)
        self.spatial_index = index.Index()
        self.file_metadata: Dict[int, Dict] = {}
        self.file_counter = 0
        
        logger.info(f"📁 Dossier MNT: {self.mnt_directory}")
        logger.info(f"📋 Fichier index: {self.index_file}")
    
    def scanner_fichiers_mnt(self) -> List[Path]:
        """Scanner tous les fichiers TIFF dans le dossier MNT"""
        fichiers_tiff = []
        
        if not self.mnt_directory.exists():
            logger.error(f"Dossier MNT non trouvé: {self.mnt_directory}")
            return fichiers_tiff
        
        # Chercher récursivement tous les .tif
        for tiff_file in self.mnt_directory.rglob("*.tif"):
            if tiff_file.is_file() and tiff_file.stat().st_size > 1024:  # > 1KB
                fichiers_tiff.append(tiff_file)
        
        logger.info(f"📊 Trouvé {len(fichiers_tiff)} fichiers TIFF")
        return fichiers_tiff
    
    def extraire_metadata_fichier(self, tiff_path: Path) -> Optional[Dict]:
        """Extraire les métadonnées d'un fichier TIFF"""
        try:
            with rasterio.open(tiff_path) as dataset:
                # Métadonnées de base
                metadata = {
                    'path': str(tiff_path),
                    'nom': tiff_path.name,
                    'dossier': tiff_path.parent.name,
                    'bounds': dataset.bounds,
                    'crs': dataset.crs.to_string() if dataset.crs else 'EPSG:4326',
                    'width': dataset.width,
                    'height': dataset.height,
                    'dtype': str(dataset.dtypes[0]),
                    'nodata': dataset.nodata,
                    'resolution': dataset.res,
                    'taille_mb': tiff_path.stat().st_size / (1024 * 1024)
                }
                
                # Utiliser les bounds directement (assumant WGS84 ou proche)
                # Pour les fichiers pré-découpés, la projection est généralement correcte
                metadata['wgs84_bounds'] = dataset.bounds
                
                # Extraire info géographique du nom si possible
                nom = tiff_path.stem.lower()
                if 'n' in nom and 'w' in nom:
                    # Format type: n34_w007_rabat
                    try:
                        parts = nom.split('_')
                        if len(parts) >= 2:
                            lat_part = [p for p in parts if p.startswith('n')][0]
                            lon_part = [p for p in parts if p.startswith('w')][0]
                            
                            lat_center = int(lat_part[1:])
                            lon_center = -int(lon_part[1:])  # Ouest = négatif
                            
                            metadata['center_lat'] = lat_center
                            metadata['center_lon'] = lon_center
                            metadata['zone_info'] = parts[2] if len(parts) > 2 else 'unknown'
                    except:
                        pass
                
                return metadata
                
        except Exception as e:
            logger.warning(f"Erreur lecture {tiff_path.name}: {e}")
            return None
    
    def construire_index_spatial(self) -> bool:
        """Construire l'index spatial R-tree"""
        logger.info("🔨 Construction de l'index spatial...")
        
        fichiers_tiff = self.scanner_fichiers_mnt()
        if not fichiers_tiff:
            logger.error("❌ Aucun fichier TIFF trouvé")
            return False
        
        fichiers_indexes = 0
        taille_totale_mb = 0
        
        for tiff_file in fichiers_tiff:
            metadata = self.extraire_metadata_fichier(tiff_file)
            
            if metadata:
                # Ajouter à l'index spatial
                bounds = metadata['wgs84_bounds']
                self.spatial_index.insert(self.file_counter, bounds)
                
                # Stocker les métadonnées
                self.file_metadata[self.file_counter] = metadata
                
                fichiers_indexes += 1
                taille_totale_mb += metadata['taille_mb']
                
                logger.debug(f"✅ Indexé: {tiff_file.name} ({metadata['taille_mb']:.1f}MB)")
                self.file_counter += 1
            else:
                logger.warning(f"⚠️ Ignoré: {tiff_file.name}")
        
        logger.info(f"📊 Index créé: {fichiers_indexes} fichiers, {taille_totale_mb:.1f}MB total")
        return fichiers_indexes > 0
    
    def sauvegarder_index(self) -> bool:
        """Sauvegarder l'index sur disque"""
        try:
            # Préparer les données
            index_data = {
                'file_metadata': self.file_metadata,
                'file_counter': self.file_counter,
                'mnt_directory': str(self.mnt_directory),
                'stats': {
                    'total_files': len(self.file_metadata),
                    'total_size_mb': sum(m['taille_mb'] for m in self.file_metadata.values()),
                    'zones': list(set(m.get('dossier', 'unknown') for m in self.file_metadata.values()))
                }
            }
            
            # Sauvegarder métadonnées
            with open(self.index_file, 'wb') as f:
                pickle.dump(index_data, f)
            
            # Sauvegarder index spatial
            spatial_index_file = self.index_file.with_suffix('.idx')
            self.spatial_index.close()
            
            # Recréer et sauvegarder l'index spatial
            self.spatial_index = index.Index(str(spatial_index_file))
            for file_id, metadata in self.file_metadata.items():
                bounds = metadata['wgs84_bounds']
                self.spatial_index.insert(file_id, bounds)
            
            logger.info(f"💾 Index sauvegardé: {self.index_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def afficher_statistiques(self):
        """Afficher les statistiques de l'index"""
        if not self.file_metadata:
            logger.warning("Aucune donnée à afficher")
            return
        
        total_files = len(self.file_metadata)
        total_size_mb = sum(m['taille_mb'] for m in self.file_metadata.values())
        
        # Grouper par dossier/zone
        zones = {}
        for metadata in self.file_metadata.values():
            zone = metadata.get('dossier', 'unknown')
            if zone not in zones:
                zones[zone] = {'count': 0, 'size_mb': 0}
            zones[zone]['count'] += 1
            zones[zone]['size_mb'] += metadata['taille_mb']
        
        print(f"\n📊 STATISTIQUES INDEX MNT:")
        print(f"   📁 Total fichiers: {total_files}")
        print(f"   💾 Taille totale: {total_size_mb:.1f} MB")
        print(f"   📊 Taille moyenne: {total_size_mb/total_files:.1f} MB/fichier")
        print(f"   🗺️ Zones couvertes: {len(zones)}")
        
        print(f"\n📋 RÉPARTITION PAR ZONE:")
        for zone, stats in sorted(zones.items()):
            print(f"   🗂️ {zone}: {stats['count']} fichiers, {stats['size_mb']:.1f} MB")
    
    def tester_requete_spatiale(self):
        """Tester une requête spatiale simple"""
        # Test avec les coordonnées de Rabat
        test_lon, test_lat = -6.8498, 33.9716
        
        logger.info(f"🧪 Test requête spatiale: Rabat ({test_lon}, {test_lat})")
        
        # Chercher les fichiers intersectant ce point
        file_ids = list(self.spatial_index.intersection((test_lon, test_lat, test_lon, test_lat)))
        
        if file_ids:
            logger.info(f"✅ Trouvé {len(file_ids)} fichier(s) pour Rabat:")
            for file_id in file_ids[:3]:  # Afficher max 3
                metadata = self.file_metadata[file_id]
                logger.info(f"   📄 {metadata['nom']} ({metadata['taille_mb']:.1f}MB)")
        else:
            logger.warning("❌ Aucun fichier trouvé pour Rabat")

def main():
    """Fonction principale"""
    logger.info("🗺️ INDEXATION MNT SIMPLIFIÉ POUR C2-EW")
    logger.info("=" * 50)
    
    # Créer l'indexeur
    indexeur = IndexeurMNTSimple()
    
    # Construire l'index
    if indexeur.construire_index_spatial():
        # Sauvegarder
        if indexeur.sauvegarder_index():
            indexeur.afficher_statistiques()
            indexeur.tester_requete_spatiale()
            
            logger.info("\n✅ INDEXATION TERMINÉE AVEC SUCCÈS!")
            logger.info("🎯 L'outil de visibilité optique est prêt!")
            logger.info("🚀 Démarrez le backend: cd backend && uvicorn app.main:app --reload")
        else:
            logger.error("❌ Erreur lors de la sauvegarde")
            return False
    else:
        logger.error("❌ Erreur lors de la construction de l'index")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
