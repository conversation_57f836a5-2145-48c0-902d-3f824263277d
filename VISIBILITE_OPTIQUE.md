# 👁️ Outil de Visibilité Optique C2-EW

## Vue d'ensemble

L'outil de visibilité optique permet de calculer en temps réel si un observateur peut voir une cible en tenant compte du relief du terrain. Il utilise les données MNT (Modèle Numérique de Terrain) pour effectuer des calculs précis de ligne de vue.

## 🚀 Installation et Configuration

### 1. Prérequis

```bash
# Installer les dépendances géospatiales
pip install rasterio rtree pyproj

# Ou utiliser le fichier requirements.txt mis à jour
cd backend
pip install -r requirements.txt
```

### 2. Initialisation de l'index MNT

```bash
# Exécuter le script d'initialisation (une seule fois)
python scripts/init_mnt_index.py
```

Ce script va :
- Scanner tous les fichiers TIFF dans `/backend/app/MNT/`
- Créer un index spatial R-tree pour optimiser les requêtes
- Sauvegarder l'index sur disque (`/backend/app/mnt_index.pkl`)

### 3. Démarrage du backend

```bash
cd backend
uvicorn app.main:app --reload
```

L'API de ligne de vue sera disponible sur `/api/v1/los/`

## 🎯 Utilisation

### Interface Utilisateur

1. **Activer l'outil** : Cliquer sur "Visibilité Optique" dans la sidebar gauche
2. **Définir l'observateur** : Premier clic sur la carte
3. **Définir la cible** : Déplacer la souris ou cliquer pour la position finale
4. **Résultat instantané** : Ligne colorée et distance affichées

### Contrôles

- **Clic gauche** : Définir observateur puis cible
- **Déplacement souris** : Mise à jour temps réel de la cible
- **Shift + Clic** : Redéfinir uniquement la cible
- **Clic droit** : Réinitialiser les positions
- **Bouton fermer** : Désactiver l'outil

### Interprétation des Résultats

- **🟢 Ligne verte** : Vue libre entre observateur et cible
- **🔴 Ligne rouge** : Vue obstruée par le terrain
- **Distance** : Affichée en mètres ou kilomètres
- **Détails** : Informations sur l'obstruction si applicable

## 🔧 Architecture Technique

### Backend (FastAPI)

#### Index Spatial
- **R-tree** : Index spatial des fichiers GeoTIFF
- **Cache** : Handles de fichiers en mémoire (max 10 fichiers)
- **Lecture partielle** : Extraction windowed avec rasterio

#### API Endpoints

```
POST /api/v1/los/line-of-sight
POST /api/v1/los/elevation
GET  /api/v1/los/mnt-stats
POST /api/v1/los/clear-cache
POST /api/v1/los/reload-index
```

#### Optimisations Performance

- **Échantillonnage adaptatif** : 1 point tous les 10m, max 200 points
- **Cache des résultats** : 50 calculs récents, TTL 30s
- **Calculs asynchrones** : Non-bloquant avec AbortController
- **Debounce** : 100ms pour éviter les calculs excessifs

### Frontend (React)

#### Hook `useLineOfSight`
- Gestion d'état de l'outil
- Cache côté client
- Debounce des requêtes API
- Calculs de distance

#### Composant `LineOfSightTool`
- Interaction avec la carte Leaflet
- Marqueurs personnalisés
- Ligne de vue colorée
- Interface utilisateur

## 📊 Performance

### Optimisations Implémentées

1. **Index spatial R-tree** : Requête O(log n) au lieu de O(n)
2. **Cache de fichiers** : Évite les ouvertures/fermetures répétées
3. **Lecture windowed** : Charge uniquement les données nécessaires
4. **Échantillonnage intelligent** : Adapte le nombre de points à la distance
5. **Cache des résultats** : Évite les recalculs identiques
6. **Debounce** : Limite les appels API pendant le mouvement de souris

### Métriques Typiques

- **Temps de réponse** : < 200ms pour distances < 10km
- **Mémoire** : ~50MB pour 10 fichiers TIFF en cache
- **Précision** : 1 point d'élévation tous les 10m
- **Cache hit rate** : ~80% pour les mouvements de souris

## 🛠️ Configuration Avancée

### Variables d'Environnement

```bash
# Chemin vers les fichiers MNT
MNT_DIRECTORY=/app/MNT

# Taille du cache de fichiers
MNT_CACHE_SIZE=10

# Intervalle d'échantillonnage (mètres)
LOS_SAMPLE_INTERVAL=10

# Nombre maximum de points
LOS_MAX_POINTS=200
```

### Paramètres de Calcul

```python
# Dans le hook useLineOfSight
DEBOUNCE_DELAY = 100  # ms
CACHE_SIZE = 50       # nombre de résultats
CACHE_TTL = 30000     # ms (30 secondes)
```

## 🔍 Dépannage

### Problèmes Courants

#### "Service MNT non disponible"
- Vérifier que l'index a été initialisé : `python scripts/init_mnt_index.py`
- Vérifier les fichiers TIFF dans `/backend/app/MNT/`

#### "Données d'élévation insuffisantes"
- Zone non couverte par les fichiers MNT
- Fichiers TIFF corrompus ou format incorrect

#### Performances lentes
- Réduire `LOS_MAX_POINTS` dans le code
- Augmenter `DEBOUNCE_DELAY`
- Vérifier la taille du cache

### Logs et Debug

```bash
# Activer les logs détaillés
export LOG_LEVEL=DEBUG

# Vérifier les statistiques MNT
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8000/api/v1/los/mnt-stats
```

## 📈 Évolutions Futures

### Améliorations Prévues

1. **Courbure terrestre** : Prise en compte pour grandes distances
2. **Réfraction atmosphérique** : Correction des effets atmosphériques
3. **Zones de Fresnel** : Calcul des zones de dégagement radio
4. **Export des profils** : Sauvegarde des calculs
5. **Analyse batch** : Calculs multiples automatisés

### Intégrations Possibles

- **Planification de missions** : Positionnement optimal d'équipements
- **Analyse de couverture** : Zones visibles depuis un point
- **Simulation radio** : Propagation des ondes
- **Intelligence géospatiale** : Analyse de terrain tactique

## 📝 API Reference

### Calcul de Ligne de Vue

```javascript
POST /api/v1/los/line-of-sight
{
  "observer": {"lon": -6.8498, "lat": 33.9716},
  "target": {"lon": -6.8400, "lat": 33.9800},
  "observer_height": 2.0,
  "target_height": 0.0
}

// Réponse
{
  "visible": true,
  "distance_m": 1247.3,
  "observer_elevation_m": 245.2,
  "target_elevation_m": 251.8,
  "calculation_time_ms": 156
}
```

### Élévation Ponctuelle

```javascript
POST /api/v1/los/elevation
{
  "point": {"lon": -6.8498, "lat": 33.9716}
}

// Réponse
{
  "elevation_m": 245.2,
  "point": {"lon": -6.8498, "lat": 33.9716}
}
```

---

**Note** : Cet outil est optimisé pour les besoins opérationnels militaires avec un focus sur la performance et la précision.
