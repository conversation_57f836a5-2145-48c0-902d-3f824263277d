#!/usr/bin/env python3
"""
Téléchargement tuiles zoom 6 - Région étendue
Limites: Ouargla, Misrata, Niger, Sabha + 489km à gauche de Funchal + 516km à gauche de Santa Cruz de Tenerife
"""

import os
import requests
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def download_tile(tile_info):
    """Télécharge une tuile"""
    x, y, z, source_url, base_dir, source_name = tile_info
    
    tile_dir = os.path.join(base_dir, source_name, str(z), str(x))
    tile_path = os.path.join(tile_dir, f"{y}.png")
    
    # Vérifier si existe déjà
    if os.path.exists(tile_path):
        return f"Existe: {z}/{x}/{y}"
    
    url = source_url.format(x=x, y=y, z=z)
    
    try:
        os.makedirs(tile_dir, exist_ok=True)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        with open(tile_path, 'wb') as f:
            f.write(response.content)
        
        return f"✓ {z}/{x}/{y}"
        
    except Exception as e:
        return f"✗ {z}/{x}/{y}: {str(e)}"

def calculate_distance_km(lat1, lon1, lat2, lon2):
    """Calcule la distance entre deux points en km"""
    R = 6371  # Rayon de la Terre en km
    dlat = math.radians(lat2 - lat1)
    dlon = math.radians(lon2 - lon1)
    a = (math.sin(dlat/2) * math.sin(dlat/2) + 
         math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
         math.sin(dlon/2) * math.sin(dlon/2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    return R * c

def calculate_point_at_distance(lat, lon, bearing_deg, distance_km):
    """Calcule un point à une distance donnée dans une direction"""
    R = 6371  # Rayon de la Terre en km
    bearing = math.radians(bearing_deg)
    lat1 = math.radians(lat)
    lon1 = math.radians(lon)
    
    lat2 = math.asin(math.sin(lat1) * math.cos(distance_km/R) +
                     math.cos(lat1) * math.sin(distance_km/R) * math.cos(bearing))
    
    lon2 = lon1 + math.atan2(math.sin(bearing) * math.sin(distance_km/R) * math.cos(lat1),
                             math.cos(distance_km/R) - math.sin(lat1) * math.sin(lat2))
    
    return math.degrees(lat2), math.degrees(lon2)

def main():
    print("=" * 80)
    print("  TÉLÉCHARGEMENT TUILES ZOOM 6 - RÉGION ÉTENDUE")
    print("=" * 80)
    
    base_dir = os.path.join(os.path.dirname(__file__), "..", "tiles")
    source_name = "esri_satellite_morocco"
    source_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    
    # Points de référence
    cities = {
        'Ouargla': (31.9333, 5.3167),      # Algérie
        'Misrata': (32.3744, 15.0919),     # Libye  
        'Sabha': (27.0377, 14.4283),       # Libye Sud
        'Funchal': (32.6669, -16.9241),    # Madère
        'Santa_Cruz_Tenerife': (28.4636, -16.2518),  # Canaries
        'Agadez': (16.9716, 7.9911),       # Niger
    }
    
    print("🎯 RÉGION ÉTENDUE ZOOM 6:")
    print("   📍 Nord: Ouargla (Algérie)")
    print("   📍 Est: Misrata (Libye)")
    print("   📍 Sud-Est: Sabha (Libye)")
    print("   📍 Sud: Niger (Agadez)")
    print("   📍 Ouest: 489km à gauche de Funchal")
    print("   📍 Ouest: 516km à gauche de Santa Cruz de Tenerife")
    
    # Calcul des limites étendues
    # Point le plus à l'ouest: 489km à gauche de Funchal (direction 270°)
    funchal_lat, funchal_lon = cities['Funchal']
    west_funchal_lat, west_funchal_lon = calculate_point_at_distance(
        funchal_lat, funchal_lon, 270, 489
    )
    
    # Point le plus à l'ouest: 516km à gauche de Santa Cruz (direction 270°)
    santa_cruz_lat, santa_cruz_lon = cities['Santa_Cruz_Tenerife']
    west_santa_lat, west_santa_lon = calculate_point_at_distance(
        santa_cruz_lat, santa_cruz_lon, 270, 516
    )
    
    # Prendre le point le plus à l'ouest
    west_lon = min(west_funchal_lon, west_santa_lon)
    
    # Limites finales
    bounds = {
        'north': cities['Ouargla'][0],      # 31.93°N
        'south': cities['Agadez'][0],       # 16.97°N  
        'east': cities['Misrata'][1],       # 15.09°E
        'west': west_lon                    # ~-22°W
    }
    
    print(f"\n📊 LIMITES CALCULÉES:")
    print(f"   Nord:  {bounds['north']:7.3f}°N (Ouargla)")
    print(f"   Sud:   {bounds['south']:7.3f}°N (Niger)")
    print(f"   Est:   {bounds['east']:7.3f}°E (Misrata)")
    print(f"   Ouest: {bounds['west']:7.3f}°W (489km de Funchal)")
    
    # Calcul des tuiles pour zoom 6
    zoom = 6
    x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
    x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)

    # Corriger l'ordre des Y (y_min doit être plus petit que y_max)
    if y_min > y_max:
        y_min, y_max = y_max, y_min

    # Ajuster les limites
    x_min = max(0, x_min)
    x_max = min(2**zoom - 1, x_max)
    y_min = max(0, y_min)
    y_max = min(2**zoom - 1, y_max)
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    
    print(f"\n📈 CALCUL TUILES ZOOM {zoom}:")
    print(f"   X: {x_min} → {x_max} ({x_max - x_min + 1} tuiles)")
    print(f"   Y: {y_min} → {y_max} ({y_max - y_min + 1} tuiles)")
    print(f"   Total: {total_tiles} tuiles")
    
    # Vérification de la taille
    estimated_size_mb = total_tiles * 0.025  # ~25KB par tuile
    print(f"   Taille estimée: {estimated_size_mb:.1f} MB")
    
    if total_tiles > 1000:
        print(f"\n⚠️  ATTENTION: {total_tiles} tuiles à télécharger!")
        response = input("Continuer? (o/N): ")
        if response.lower() != 'o':
            print("Téléchargement annulé.")
            return
    
    # Préparer la liste des tuiles
    all_tiles = []
    for x in range(x_min, x_max + 1):
        for y in range(y_min, y_max + 1):
            all_tiles.append((x, y, zoom, source_url, base_dir, source_name))
    
    print(f"\n🚀 DÉBUT DU TÉLÉCHARGEMENT...")
    print(f"   Source: {source_name}")
    print(f"   Zoom: {zoom}")
    print(f"   Tuiles: {len(all_tiles)}")
    
    # Téléchargement avec barre de progression
    start_time = time.time()
    downloaded = 0
    errors = 0
    existing = 0
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        with tqdm(total=len(all_tiles), desc="Téléchargement") as pbar:
            future_to_tile = {executor.submit(download_tile, tile): tile for tile in all_tiles}
            
            for future in as_completed(future_to_tile):
                result = future.result()
                pbar.update(1)
                
                if result.startswith("✓"):
                    downloaded += 1
                elif result.startswith("Existe"):
                    existing += 1
                else:
                    errors += 1
                
                # Mise à jour de la description
                pbar.set_description(f"✓{downloaded} Existe:{existing} ✗{errors}")
                
                time.sleep(0.1)  # Délai pour éviter la surcharge
    
    elapsed_time = time.time() - start_time
    
    print(f"\n✅ TÉLÉCHARGEMENT TERMINÉ!")
    print(f"   Temps: {elapsed_time:.1f}s")
    print(f"   Téléchargées: {downloaded}")
    print(f"   Existantes: {existing}")
    print(f"   Erreurs: {errors}")
    print(f"   Total: {downloaded + existing + errors}")
    
    if downloaded > 0:
        print(f"\n📁 Tuiles sauvées dans: {os.path.join(base_dir, source_name)}")
        print(f"🌐 URL serveur: http://localhost:8000/tiles/{source_name}/{{z}}/{{x}}/{{y}}.png")

if __name__ == "__main__":
    main()
