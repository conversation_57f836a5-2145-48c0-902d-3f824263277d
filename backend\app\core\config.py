"""
Configuration settings for the C2-EW application
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "C2-EW Platform"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # API
    api_v1_str: str = "/api/v1"
    
    # Database
    database_url: Optional[str] = None
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    access_token_expire_minutes: int = 30
    
    # CORS
    backend_cors_origins: list = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001"
    ]
    
    # MNT (Digital Terrain Model)
    mnt_data_path: str = "app/MNT"
    mnt_index_path: str = "app/mnt_index.pkl"
    
    # Tiles
    tiles_path: str = "tiles"
    
    # Line of Sight
    los_max_distance_km: float = 100.0
    los_sample_interval_m: float = 10.0
    los_max_samples: int = 200
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
