-- =====================================================
-- STRUCTURE BASE DE DONNÉES MNT POUR VISIBILITÉ OPTIQUE
-- Expert SIG - Optimisé pour performance et précision
-- =====================================================

-- Extension PostGIS obligatoire
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- Table principale MNT avec optimisations
CREATE TABLE IF NOT EXISTS mnt_data (
    id BIGSERIAL PRIMARY KEY,
    geom GEOMETRY(POINT, 4326) NOT NULL,
    elevation REAL NOT NULL,
    source_file VARCHAR(255),
    resolution_m REAL DEFAULT 30.0,
    accuracy_class SMALLINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Contraintes de validation
    CONSTRAINT valid_elevation CHECK (elevation >= -500 AND elevation <= 5000),
    CONSTRAINT valid_geom CHECK (ST_IsValid(geom)),
    CONSTRAINT morocco_bounds CHECK (
        ST_X(geom) BETWEEN -18.0 AND -1.0 AND 
        ST_Y(geom) BETWEEN 20.0 AND 36.0
    )
);

-- Index spatial principal (CRITIQUE pour performance)
CREATE INDEX IF NOT EXISTS idx_mnt_geom_gist 
ON mnt_data USING GIST (geom);

-- Index sur l'élévation pour requêtes de profil
CREATE INDEX IF NOT EXISTS idx_mnt_elevation 
ON mnt_data (elevation);

-- Index composé pour requêtes spatiales avec élévation
CREATE INDEX IF NOT EXISTS idx_mnt_geom_elevation 
ON mnt_data USING GIST (geom) INCLUDE (elevation);

-- Table de cache pour calculs de visibilité
CREATE TABLE IF NOT EXISTS visibility_cache (
    id BIGSERIAL PRIMARY KEY,
    point1_geom GEOMETRY(POINT, 4326) NOT NULL,
    point2_geom GEOMETRY(POINT, 4326) NOT NULL,
    distance_m REAL NOT NULL,
    is_visible BOOLEAN NOT NULL,
    obstruction_distance_m REAL,
    calculation_time_ms INTEGER,
    profile_hash VARCHAR(64),
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '1 hour'),
    
    -- Index pour recherche rapide
    CONSTRAINT unique_visibility_pair UNIQUE (
        ST_X(point1_geom), ST_Y(point1_geom),
        ST_X(point2_geom), ST_Y(point2_geom)
    )
);

-- Index pour le cache de visibilité
CREATE INDEX IF NOT EXISTS idx_visibility_cache_geoms 
ON visibility_cache USING GIST (point1_geom, point2_geom);

CREATE INDEX IF NOT EXISTS idx_visibility_cache_expires 
ON visibility_cache (expires_at);

-- Table de métadonnées des fichiers MNT
CREATE TABLE IF NOT EXISTS mnt_files_metadata (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL UNIQUE,
    bounds GEOMETRY(POLYGON, 4326) NOT NULL,
    resolution_m REAL NOT NULL,
    crs_code INTEGER,
    nodata_value REAL,
    min_elevation REAL,
    max_elevation REAL,
    point_count BIGINT,
    file_size_mb REAL,
    checksum VARCHAR(64),
    imported_at TIMESTAMP DEFAULT NOW(),
    
    -- Index spatial sur les bounds
    CONSTRAINT valid_bounds CHECK (ST_IsValid(bounds))
);

CREATE INDEX IF NOT EXISTS idx_mnt_files_bounds 
ON mnt_files_metadata USING GIST (bounds);

-- =====================================================
-- FONCTIONS POSTGIS OPTIMISÉES
-- =====================================================

-- Fonction d'interpolation bilinéaire optimisée
CREATE OR REPLACE FUNCTION get_elevation_interpolated(
    target_lon DOUBLE PRECISION,
    target_lat DOUBLE PRECISION,
    search_radius_m DOUBLE PRECISION DEFAULT 100.0
) RETURNS REAL AS $$
DECLARE
    result_elevation REAL;
    point_geom GEOMETRY;
BEGIN
    -- Créer le point de recherche
    point_geom := ST_SetSRID(ST_MakePoint(target_lon, target_lat), 4326);
    
    -- Interpolation bilinéaire avec les 4 points les plus proches
    WITH nearest_points AS (
        SELECT 
            elevation,
            ST_Distance(geom, point_geom) as distance,
            ST_X(geom) as x,
            ST_Y(geom) as y
        FROM mnt_data
        WHERE ST_DWithin(geom, point_geom, search_radius_m / 111320.0) -- Conversion approximative m -> degrés
        ORDER BY geom <-> point_geom
        LIMIT 4
    ),
    weighted_avg AS (
        SELECT 
            SUM(elevation / NULLIF(distance, 0)) / SUM(1.0 / NULLIF(distance, 0)) as interpolated_elevation
        FROM nearest_points
        WHERE distance > 0
    )
    SELECT interpolated_elevation INTO result_elevation FROM weighted_avg;
    
    -- Fallback: moyenne simple si interpolation échoue
    IF result_elevation IS NULL THEN
        SELECT AVG(elevation) INTO result_elevation
        FROM mnt_data
        WHERE ST_DWithin(geom, point_geom, search_radius_m / 111320.0)
        LIMIT 10;
    END IF;
    
    RETURN COALESCE(result_elevation, 0.0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Fonction de calcul de profil d'élévation optimisée
CREATE OR REPLACE FUNCTION get_elevation_profile(
    start_lon DOUBLE PRECISION,
    start_lat DOUBLE PRECISION,
    end_lon DOUBLE PRECISION,
    end_lat DOUBLE PRECISION,
    sample_interval_m DOUBLE PRECISION DEFAULT 25.0
) RETURNS TABLE(
    distance_m REAL,
    elevation_m REAL,
    lon DOUBLE PRECISION,
    lat DOUBLE PRECISION
) AS $$
DECLARE
    total_distance_m REAL;
    num_samples INTEGER;
    i INTEGER;
    current_ratio DOUBLE PRECISION;
    current_lon DOUBLE PRECISION;
    current_lat DOUBLE PRECISION;
    current_elevation REAL;
BEGIN
    -- Calculer la distance totale (Haversine)
    total_distance_m := ST_Distance(
        ST_Transform(ST_SetSRID(ST_MakePoint(start_lon, start_lat), 4326), 3857),
        ST_Transform(ST_SetSRID(ST_MakePoint(end_lon, end_lat), 4326), 3857)
    );
    
    -- Calculer le nombre d'échantillons
    num_samples := GREATEST(2, LEAST(200, CEIL(total_distance_m / sample_interval_m)));
    
    -- Générer le profil
    FOR i IN 0..num_samples LOOP
        current_ratio := i::DOUBLE PRECISION / num_samples::DOUBLE PRECISION;
        
        -- Position interpolée
        current_lon := start_lon + (end_lon - start_lon) * current_ratio;
        current_lat := start_lat + (end_lat - start_lat) * current_ratio;
        
        -- Élévation interpolée
        current_elevation := get_elevation_interpolated(current_lon, current_lat);
        
        -- Retourner le point du profil
        distance_m := total_distance_m * current_ratio;
        elevation_m := current_elevation;
        lon := current_lon;
        lat := current_lat;
        
        RETURN NEXT;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql STABLE;

-- Fonction de calcul de visibilité Line of Sight
CREATE OR REPLACE FUNCTION calculate_line_of_sight(
    observer_lon DOUBLE PRECISION,
    observer_lat DOUBLE PRECISION,
    target_lon DOUBLE PRECISION,
    target_lat DOUBLE PRECISION,
    observer_height_m REAL DEFAULT 2.0,
    target_height_m REAL DEFAULT 0.0,
    sample_interval_m REAL DEFAULT 25.0
) RETURNS TABLE(
    is_visible BOOLEAN,
    distance_m REAL,
    obstruction_distance_m REAL,
    calculation_time_ms INTEGER
) AS $$
DECLARE
    start_time TIMESTAMP;
    profile_record RECORD;
    observer_elevation REAL;
    target_elevation REAL;
    total_distance REAL;
    line_elevation REAL;
    terrain_elevation REAL;
    obstruction_found BOOLEAN := FALSE;
    obstruction_dist REAL := NULL;
BEGIN
    start_time := clock_timestamp();
    
    -- Obtenir les élévations des points de départ et d'arrivée
    observer_elevation := get_elevation_interpolated(observer_lon, observer_lat) + observer_height_m;
    target_elevation := get_elevation_interpolated(target_lon, target_lat) + target_height_m;
    
    -- Calculer la distance totale
    total_distance := ST_Distance(
        ST_Transform(ST_SetSRID(ST_MakePoint(observer_lon, observer_lat), 4326), 3857),
        ST_Transform(ST_SetSRID(ST_MakePoint(target_lon, target_lat), 4326), 3857)
    );
    
    -- Parcourir le profil d'élévation
    FOR profile_record IN 
        SELECT * FROM get_elevation_profile(
            observer_lon, observer_lat, 
            target_lon, target_lat, 
            sample_interval_m
        )
    LOOP
        -- Ignorer les points de départ et d'arrivée
        IF profile_record.distance_m > 0 AND profile_record.distance_m < total_distance THEN
            -- Calculer l'élévation attendue sur la ligne de vue
            line_elevation := observer_elevation + 
                (target_elevation - observer_elevation) * 
                (profile_record.distance_m / total_distance);
            
            terrain_elevation := profile_record.elevation_m;
            
            -- Vérifier l'obstruction
            IF terrain_elevation > line_elevation THEN
                obstruction_found := TRUE;
                obstruction_dist := profile_record.distance_m;
                EXIT; -- Sortir dès la première obstruction
            END IF;
        END IF;
    END LOOP;
    
    -- Retourner les résultats
    is_visible := NOT obstruction_found;
    distance_m := total_distance;
    obstruction_distance_m := obstruction_dist;
    calculation_time_ms := EXTRACT(MILLISECONDS FROM (clock_timestamp() - start_time))::INTEGER;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- VUES OPTIMISÉES
-- =====================================================

-- Vue pour statistiques MNT
CREATE OR REPLACE VIEW mnt_statistics AS
SELECT 
    COUNT(*) as total_points,
    MIN(elevation) as min_elevation,
    MAX(elevation) as max_elevation,
    AVG(elevation) as avg_elevation,
    STDDEV(elevation) as stddev_elevation,
    ST_Extent(geom) as bounds,
    COUNT(DISTINCT source_file) as source_files
FROM mnt_data;

-- =====================================================
-- MAINTENANCE ET OPTIMISATION
-- =====================================================

-- Fonction de nettoyage du cache
CREATE OR REPLACE FUNCTION cleanup_visibility_cache() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM visibility_cache WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Tâche de maintenance automatique (à programmer avec pg_cron)
-- SELECT cron.schedule('cleanup-visibility-cache', '*/30 * * * *', 'SELECT cleanup_visibility_cache();');

COMMENT ON TABLE mnt_data IS 'Table principale des données MNT avec optimisations spatiales';
COMMENT ON TABLE visibility_cache IS 'Cache des calculs de visibilité pour améliorer les performances';
COMMENT ON FUNCTION calculate_line_of_sight IS 'Fonction principale de calcul Line of Sight avec algorithme optimisé';
