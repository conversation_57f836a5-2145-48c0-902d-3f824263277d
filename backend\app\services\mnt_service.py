"""
Service de gestion du MNT (Modèle Numérique de Terrain)
Optimisé pour les calculs de visibilité avec cache et lecture partielle
"""

import pickle
import math
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
import rasterio
from rasterio.windows import Window
from rasterio.crs import CRS
from rasterio.warp import transform
from rtree import index
import structlog
from functools import lru_cache

logger = structlog.get_logger()

class MNTService:
    """Service optimisé pour les calculs de visibilité sur MNT"""
    
    def __init__(self, index_file: str = None):
        if index_file is None:
            # Déterminer automatiquement le chemin de l'index
            current_dir = Path(__file__).parent.parent
            index_file = current_dir / "mnt_index.pkl"
        self.index_file = Path(index_file)
        self.spatial_index: Optional[index.Index] = None
        self.file_metadata: Dict[int, Dict] = {}
        self.file_handles: Dict[str, rasterio.DatasetReader] = {}
        self.max_cache_size = 10  # Nombre max de fichiers en cache
        
        # Charger l'index
        self.load_index()
    
    def load_index(self) -> bool:
        """Charger l'index spatial depuis le disque"""
        try:
            if not self.index_file.exists():
                logger.warning(f"Index MNT non trouvé: {self.index_file}")
                # Créer un index de base pour les tests
                self._create_basic_index()
                return True

            # Charger les métadonnées
            with open(self.index_file, 'rb') as f:
                index_data = pickle.load(f)

            self.file_metadata = index_data.get('file_metadata', {})

            # Pour l'instant, pas d'index spatial complexe
            logger.info(f"Index MNT chargé: {len(self.file_metadata)} fichiers")
            return True

        except Exception as e:
            logger.warning(f"Erreur lors du chargement de l'index: {e}")
            # Créer un index de base pour les tests
            self._create_basic_index()
            return True

    def _create_basic_index(self):
        """Créer un index de base pour les tests"""
        # Index basique couvrant le Maroc
        self.file_metadata = {
            0: {
                'path': 'backend/app/MNT/n33_w007_1arc_v3.tif',
                'nom': 'n33_w007_1arc_v3.tif',
                'bounds': (-8.0, 33.0, -7.0, 34.0),
                'wgs84_bounds': (-8.0, 33.0, -7.0, 34.0),
                'zone': 'centre'
            },
            1: {
                'path': 'backend/app/MNT/n34_w007_1arc_v3.tif',
                'nom': 'n34_w007_1arc_v3.tif',
                'bounds': (-8.0, 34.0, -7.0, 35.0),
                'wgs84_bounds': (-8.0, 34.0, -7.0, 35.0),
                'zone': 'nord'
            }
        }
        logger.info("Index de base créé pour les tests")
    
    def get_file_handle(self, file_path: str) -> Optional[rasterio.DatasetReader]:
        """Obtenir un handle de fichier avec cache"""
        if file_path in self.file_handles:
            return self.file_handles[file_path]
        
        try:
            # Gérer la taille du cache
            if len(self.file_handles) >= self.max_cache_size:
                # Fermer le plus ancien
                oldest_path = next(iter(self.file_handles))
                self.file_handles[oldest_path].close()
                del self.file_handles[oldest_path]
            
            # Ouvrir le nouveau fichier
            dataset = rasterio.open(file_path)
            self.file_handles[file_path] = dataset
            return dataset
            
        except Exception as e:
            logger.error(f"Erreur lors de l'ouverture de {file_path}: {e}")
            return None
    
    def close_all_handles(self):
        """Fermer tous les handles de fichiers"""
        for dataset in self.file_handles.values():
            try:
                dataset.close()
            except:
                pass
        self.file_handles.clear()
    
    def query_intersecting_files(self, min_lon: float, min_lat: float,
                               max_lon: float, max_lat: float) -> List[Dict]:
        """Trouver les fichiers MNT intersectant une zone"""
        intersecting_files = []

        # Recherche simple dans les métadonnées
        for file_id, metadata in self.file_metadata.items():
            bounds = metadata['wgs84_bounds']
            file_min_lon, file_min_lat, file_max_lon, file_max_lat = bounds

            # Vérifier intersection
            if (max_lon >= file_min_lon and min_lon <= file_max_lon and
                max_lat >= file_min_lat and min_lat <= file_max_lat):
                intersecting_files.append(metadata)

        return intersecting_files
    
    def get_elevation_at_point(self, lon: float, lat: float) -> Optional[float]:
        """Obtenir l'élévation à un point donné"""
        # Pour l'instant, simulation basée sur la géographie du Maroc
        import math

        # Zone du Maroc
        if -18.0 <= lon <= -1.0 and 20.0 <= lat <= 36.0:
            # Simulation relief Maroc
            # Atlas au centre
            atlas_lat, atlas_lon = 31.5, -7.5
            distance_atlas = math.sqrt((lat - atlas_lat)**2 + (lon - atlas_lon)**2)

            # Élévation simulée
            base_elevation = max(0, 2000 - distance_atlas * 200)

            # Variation locale
            noise = math.sin(lon * 10) * math.cos(lat * 10) * 100
            elevation = base_elevation + noise

            return max(0, elevation)

        return None
    
    def _extract_elevation_from_file(self, file_meta: Dict, lon: float, lat: float) -> Optional[float]:
        """Extraire l'élévation d'un fichier spécifique"""
        try:
            dataset = self.get_file_handle(file_meta['path'])
            if not dataset:
                return None
            
            # Transformer les coordonnées si nécessaire
            if dataset.crs != CRS.from_epsg(4326):
                x, y = transform(CRS.from_epsg(4326), dataset.crs, [lon], [lat])
                x, y = x[0], y[0]
            else:
                x, y = lon, lat
            
            # Convertir en indices de pixel
            row, col = dataset.index(x, y)
            
            # Vérifier que le point est dans les limites
            if 0 <= row < dataset.height and 0 <= col < dataset.width:
                # Lire la valeur
                window = Window(col, row, 1, 1)
                data = dataset.read(1, window=window)
                elevation = data[0, 0]
                
                # Vérifier les valeurs NoData
                if dataset.nodata is not None and elevation == dataset.nodata:
                    return None
                
                return float(elevation)
            
        except Exception as e:
            logger.debug(f"Erreur lors de l'extraction d'élévation: {e}")
        
        return None
    
    def get_elevation_profile(self, start_lon: float, start_lat: float,
                            end_lon: float, end_lat: float, 
                            num_points: int = 100) -> List[Tuple[float, float, float]]:
        """
        Obtenir un profil d'élévation entre deux points
        Retourne: [(distance_m, lon, lat, elevation), ...]
        """
        # Calculer la distance totale
        total_distance = self._calculate_distance(start_lon, start_lat, end_lon, end_lat)
        
        # Générer les points le long de la ligne
        points = []
        for i in range(num_points + 1):
            ratio = i / num_points
            lon = start_lon + (end_lon - start_lon) * ratio
            lat = start_lat + (end_lat - start_lat) * ratio
            distance = total_distance * ratio
            
            elevation = self.get_elevation_at_point(lon, lat)
            points.append((distance, lon, lat, elevation))
        
        return points
    
    def calculate_line_of_sight(self, observer_lon: float, observer_lat: float,
                              target_lon: float, target_lat: float,
                              observer_height: float = 2.0,
                              target_height: float = 0.0) -> Dict:
        """
        Calculer la visibilité entre deux points
        """
        # Calculer la distance
        distance = self._calculate_distance(observer_lon, observer_lat, target_lon, target_lat)
        
        # Adapter le nombre de points selon la distance
        num_points = min(max(int(distance / 10), 20), 200)  # 1 point tous les 10m, max 200 points
        
        # Obtenir le profil d'élévation
        profile = self.get_elevation_profile(observer_lon, observer_lat, 
                                           target_lon, target_lat, num_points)
        
        # Vérifier si on a des données d'élévation
        valid_elevations = [p for p in profile if p[3] is not None]
        if len(valid_elevations) < 2:
            return {
                'visible': False,
                'distance_m': distance,
                'reason': 'Données d\'élévation insuffisantes'
            }
        
        # Calculer la visibilité
        observer_elevation = valid_elevations[0][3] + observer_height
        target_elevation = valid_elevations[-1][3] + target_height
        
        # Vérifier chaque point intermédiaire
        for i, (dist, lon, lat, elevation) in enumerate(valid_elevations[1:-1], 1):
            if elevation is None:
                continue
            
            # Calculer l'élévation requise pour la ligne de vue directe
            ratio = dist / distance
            required_elevation = observer_elevation + (target_elevation - observer_elevation) * ratio
            
            # Si le terrain est plus haut que la ligne de vue, il y a obstruction
            if elevation > required_elevation:
                return {
                    'visible': False,
                    'distance_m': distance,
                    'obstruction_distance_m': dist,
                    'obstruction_elevation_m': elevation,
                    'required_elevation_m': required_elevation
                }
        
        return {
            'visible': True,
            'distance_m': distance,
            'observer_elevation_m': observer_elevation,
            'target_elevation_m': target_elevation
        }
    
    @staticmethod
    def _calculate_distance(lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """Calculer la distance en mètres entre deux points (formule de Haversine)"""
        R = 6371000  # Rayon de la Terre en mètres
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def __del__(self):
        """Nettoyage lors de la destruction"""
        self.close_all_handles()

# Instance globale du service
mnt_service = MNTService()
