#!/usr/bin/env python3
"""
Service de Visibilité Optique Professionnel
Expert SIG - Optimisé pour précision et performance
"""

import asyncio
import hashlib
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import math

import asyncpg
import redis.asyncio as redis
from fastapi import HTTPException
import structlog

logger = structlog.get_logger()

@dataclass
class VisibilityResult:
    """Résultat de calcul de visibilité"""
    is_visible: bool
    distance_km: float
    distance_m: float
    obstruction_distance_m: Optional[float] = None
    calculation_time_ms: int = 0
    profile_data: Optional[List[Dict]] = None
    cache_hit: bool = False
    accuracy_class: str = "HIGH"

@dataclass
class Point:
    """Point géographique avec validation"""
    lat: float
    lon: float
    elevation: Optional[float] = None
    
    def __post_init__(self):
        # Validation des coordonnées pour le Maroc
        if not (-18.0 <= self.lon <= -1.0):
            raise ValueError(f"Longitude {self.lon} hors limites Maroc")
        if not (20.0 <= self.lat <= 36.0):
            raise ValueError(f"Latitude {self.lat} hors limites Maroc")

class VisibilityService:
    """Service professionnel de calcul de visibilité optique"""
    
    def __init__(self, db_pool: asyncpg.Pool, redis_client: redis.Redis):
        self.db_pool = db_pool
        self.redis_client = redis_client
        self.cache_ttl = 3600  # 1 heure
        
        # Paramètres d'optimisation
        self.max_distance_km = 100.0
        self.min_sample_interval_m = 10.0
        self.max_sample_interval_m = 50.0
        self.max_samples = 200
        
        logger.info("Service de visibilité optique initialisé")
    
    async def calculate_visibility(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float = 2.0,
        target_height_m: float = 0.0,
        use_cache: bool = True
    ) -> VisibilityResult:
        """
        Calcul principal de visibilité optique
        Algorithme Line of Sight (LOS) optimisé
        """
        start_time = time.time()
        
        try:
            # Validation des entrées
            self._validate_inputs(point1, point2, observer_height_m, target_height_m)
            
            # Vérifier le cache Redis
            if use_cache:
                cached_result = await self._get_cached_result(
                    point1, point2, observer_height_m, target_height_m
                )
                if cached_result:
                    cached_result.cache_hit = True
                    return cached_result
            
            # Calcul de la distance
            distance_m = self._calculate_distance_haversine(point1, point2)
            distance_km = distance_m / 1000.0
            
            # Vérification des limites
            if distance_km > self.max_distance_km:
                raise HTTPException(
                    status_code=400,
                    detail=f"Distance {distance_km:.1f}km dépasse la limite de {self.max_distance_km}km"
                )
            
            # Calcul via PostgreSQL optimisé
            result = await self._calculate_los_postgresql(
                point1, point2, observer_height_m, target_height_m, distance_m
            )
            
            calculation_time_ms = int((time.time() - start_time) * 1000)
            
            # Construire le résultat
            visibility_result = VisibilityResult(
                is_visible=result['is_visible'],
                distance_km=distance_km,
                distance_m=distance_m,
                obstruction_distance_m=result.get('obstruction_distance_m'),
                calculation_time_ms=calculation_time_ms,
                profile_data=result.get('profile_data'),
                cache_hit=False,
                accuracy_class=self._determine_accuracy_class(distance_km, calculation_time_ms)
            )
            
            # Mettre en cache si performance acceptable
            if use_cache and calculation_time_ms < 500:
                await self._cache_result(
                    point1, point2, observer_height_m, target_height_m, visibility_result
                )
            
            # Logging pour monitoring
            logger.info(
                "Calcul visibilité terminé",
                distance_km=distance_km,
                is_visible=result['is_visible'],
                calculation_time_ms=calculation_time_ms,
                cache_hit=False
            )
            
            return visibility_result
            
        except Exception as e:
            calculation_time_ms = int((time.time() - start_time) * 1000)
            logger.error(
                "Erreur calcul visibilité",
                error=str(e),
                point1=f"{point1.lat},{point1.lon}",
                point2=f"{point2.lat},{point2.lon}",
                calculation_time_ms=calculation_time_ms
            )
            raise HTTPException(status_code=500, detail=f"Erreur calcul visibilité: {str(e)}")
    
    async def _calculate_los_postgresql(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float,
        target_height_m: float,
        distance_m: float
    ) -> Dict[str, Any]:
        """Calcul Line of Sight via PostgreSQL optimisé"""
        
        # Déterminer l'intervalle d'échantillonnage adaptatif
        if distance_m < 1000:  # < 1km
            sample_interval = self.min_sample_interval_m
        elif distance_m < 10000:  # < 10km
            sample_interval = 25.0
        else:
            sample_interval = self.max_sample_interval_m
        
        async with self.db_pool.acquire() as conn:
            # Appel de la fonction PostgreSQL optimisée
            result = await conn.fetchrow("""
                SELECT 
                    is_visible,
                    distance_m,
                    obstruction_distance_m,
                    calculation_time_ms
                FROM calculate_line_of_sight($1, $2, $3, $4, $5, $6, $7)
            """, 
                point1.lon, point1.lat,  # Observer
                point2.lon, point2.lat,  # Target
                observer_height_m,
                target_height_m,
                sample_interval
            )
            
            if not result:
                raise Exception("Aucun résultat retourné par la fonction PostgreSQL")
            
            return {
                'is_visible': result['is_visible'],
                'distance_m': result['distance_m'],
                'obstruction_distance_m': result['obstruction_distance_m'],
                'calculation_time_ms': result['calculation_time_ms']
            }
    
    async def get_elevation(self, point: Point) -> float:
        """Obtenir l'élévation interpolée d'un point"""
        async with self.db_pool.acquire() as conn:
            elevation = await conn.fetchval("""
                SELECT get_elevation_interpolated($1, $2, $3)
            """, point.lon, point.lat, 100.0)  # Rayon de recherche 100m
            
            return float(elevation) if elevation is not None else 0.0
    
    def _validate_inputs(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float,
        target_height_m: float
    ):
        """Validation stricte des entrées"""
        if not (0.0 <= observer_height_m <= 1000.0):
            raise ValueError(f"Hauteur observateur {observer_height_m}m invalide")
        
        if not (0.0 <= target_height_m <= 1000.0):
            raise ValueError(f"Hauteur cible {target_height_m}m invalide")
        
        # Vérifier que les points ne sont pas identiques
        if abs(point1.lat - point2.lat) < 1e-6 and abs(point1.lon - point2.lon) < 1e-6:
            raise ValueError("Les points de départ et d'arrivée sont identiques")
    
    def _calculate_distance_haversine(self, point1: Point, point2: Point) -> float:
        """Calcul de distance précis (Haversine)"""
        R = 6371000.0  # Rayon de la Terre en mètres
        
        lat1_rad = math.radians(point1.lat)
        lat2_rad = math.radians(point2.lat)
        delta_lat = math.radians(point2.lat - point1.lat)
        delta_lon = math.radians(point2.lon - point1.lon)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def _determine_accuracy_class(self, distance_km: float, calculation_time_ms: int) -> str:
        """Déterminer la classe de précision"""
        if distance_km < 1.0 and calculation_time_ms < 100:
            return "VERY_HIGH"
        elif distance_km < 10.0 and calculation_time_ms < 200:
            return "HIGH"
        elif distance_km < 50.0 and calculation_time_ms < 500:
            return "MEDIUM"
        else:
            return "STANDARD"
    
    async def _get_cached_result(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float,
        target_height_m: float
    ) -> Optional[VisibilityResult]:
        """Récupérer un résultat du cache Redis"""
        try:
            cache_key = self._generate_cache_key(point1, point2, observer_height_m, target_height_m)
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                import json
                data = json.loads(cached_data)
                return VisibilityResult(**data)
            
        except Exception as e:
            logger.warning("Erreur lecture cache", error=str(e))
        
        return None
    
    async def _cache_result(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float,
        target_height_m: float,
        result: VisibilityResult
    ):
        """Mettre en cache un résultat"""
        try:
            cache_key = self._generate_cache_key(point1, point2, observer_height_m, target_height_m)
            
            # Sérialiser le résultat (sans profile_data pour économiser l'espace)
            cache_data = {
                'is_visible': result.is_visible,
                'distance_km': result.distance_km,
                'distance_m': result.distance_m,
                'obstruction_distance_m': result.obstruction_distance_m,
                'calculation_time_ms': result.calculation_time_ms,
                'accuracy_class': result.accuracy_class
            }
            
            import json
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.warning("Erreur mise en cache", error=str(e))
    
    def _generate_cache_key(
        self,
        point1: Point,
        point2: Point,
        observer_height_m: float,
        target_height_m: float
    ) -> str:
        """Générer une clé de cache unique"""
        # Arrondir les coordonnées pour améliorer le cache hit
        lat1_rounded = round(point1.lat, 5)  # ~1m de précision
        lon1_rounded = round(point1.lon, 5)
        lat2_rounded = round(point2.lat, 5)
        lon2_rounded = round(point2.lon, 5)
        
        key_string = f"los:{lat1_rounded}:{lon1_rounded}:{lat2_rounded}:{lon2_rounded}:{observer_height_m}:{target_height_m}"
        
        # Hash pour éviter les clés trop longues
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Obtenir les statistiques du service"""
        async with self.db_pool.acquire() as conn:
            stats = await conn.fetchrow("SELECT * FROM mnt_statistics")
            
            return {
                'mnt_points': stats['total_points'],
                'elevation_range': {
                    'min': stats['min_elevation'],
                    'max': stats['max_elevation'],
                    'avg': round(stats['avg_elevation'], 1)
                },
                'coverage_bounds': stats['bounds'],
                'source_files': stats['source_files'],
                'service_status': 'OPERATIONAL'
            }
