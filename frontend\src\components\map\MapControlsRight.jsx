import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  ZoomIn,
  ZoomOut,
  Maximize,
  Minimize,
  Layers,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

const MapControlsRight = ({ mapRef }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showLayers, setShowLayers] = useState(false);

  // Fonctions de contrôle de carte
  const handleZoomIn = () => {
    if (mapRef?.current) {
      mapRef.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapRef?.current) {
      mapRef.current.zoomOut();
    }
  };

  const handleHome = () => {
    if (mapRef?.current) {
      // Retour à Sidi Yahia du Gharb
      mapRef.current.setView([34.3011, -6.1069], 11, {
        animate: true,
        duration: 1.0
      });
    }
  };

  const handleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
        // Zoom automatique en plein écran
        setTimeout(() => {
          if (mapRef?.current) {
            mapRef.current.setZoom(6);
          }
        }, 100);
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Erreur plein écran:', error);
    }
  };

  const controlButtons = [
    { id: 'zoomIn', icon: ZoomIn, action: handleZoomIn, tooltip: 'Zoom +' },
    { id: 'zoomOut', icon: ZoomOut, action: handleZoomOut, tooltip: 'Zoom -' },
    { id: 'home', icon: Home, action: handleHome, tooltip: 'Home' },
    { id: 'fullscreen', icon: Maximize, action: handleFullscreen, tooltip: 'Plein écran' },
    { id: 'layers', icon: Layers, action: () => setShowLayers(!showLayers), tooltip: 'Couches' },
  ];

  const layerCategories = [
    {
      name: 'Équipements',
      layers: [
        { id: 'comint', name: 'COMINT', enabled: true },
        { id: 'elint', name: 'ELINT', enabled: true },
        { id: 'anti_drone', name: 'Anti-Drone', enabled: true },
        { id: 'jammer', name: 'Jammer', enabled: true },
      ]
    },
    {
      name: 'Zones',
      layers: [
        { id: 'sectors', name: 'Secteurs', enabled: false },
        { id: 'interest_zones', name: 'Zones d\'intérêt', enabled: false },
        { id: 'restricted', name: 'Zones restreintes', enabled: false },
      ]
    },
    {
      name: 'Cartographie',
      layers: [
        { id: 'satellite', name: 'Satellite', enabled: true },
        { id: 'labels', name: 'Labels', enabled: true },
        { id: 'borders', name: 'Frontières', enabled: true },
      ]
    }
  ];

  return (
    <div className="fixed right-4 top-20 z-40 space-y-2">
      {/* Contrôles individuels */}
      {controlButtons.map((button) => {
        const Icon = button.icon;
        return (
          <motion.button
            key={button.id}
            onClick={button.action}
            className="w-12 h-12 bg-c2-gray-400 border border-c2-gray-300 rounded-lg flex items-center justify-center hover:bg-c2-gray-300 transition-colors shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            title={button.tooltip}
          >
            <Icon className="w-5 h-5 text-c2-white" />
          </motion.button>
        );
      })}

      {/* Panneau des couches */}
      <AnimatePresence>
        {showLayers && (
          <motion.div
            initial={{ opacity: 0, x: 10, scale: 0.95 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed right-20 top-20 w-64 bg-c2-gray-400 border border-c2-gray-300 rounded-lg shadow-lg overflow-hidden"
          >
            <div className="p-3 border-b border-c2-gray-300">
              <h3 className="text-sm font-semibold text-c2-white">Gestion des Couches</h3>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {layerCategories.map((category) => (
                <div key={category.name} className="border-b border-c2-gray-300 last:border-b-0">
                  <div className="p-3">
                    <div className="text-xs font-medium text-c2-blue mb-2">{category.name}</div>
                    <div className="space-y-2">
                      {category.layers.map((layer) => (
                        <label key={layer.id} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            defaultChecked={layer.enabled}
                            className="w-3 h-3 text-c2-blue bg-c2-gray-300 border-c2-gray-200 rounded focus:ring-c2-blue focus:ring-1"
                          />
                          <span className="text-xs text-c2-gray-100 hover:text-c2-white transition-colors">
                            {layer.name}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="p-3 border-t border-c2-gray-300 bg-c2-gray-300">
              <div className="flex space-x-2">
                <button className="flex-1 px-3 py-1 text-xs bg-c2-blue text-white rounded hover:bg-blue-600 transition-colors">
                  Appliquer
                </button>
                <button
                  onClick={() => setShowLayers(false)}
                  className="flex-1 px-3 py-1 text-xs bg-c2-gray-200 text-c2-black rounded hover:bg-gray-300 transition-colors"
                >
                  Fermer
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MapControlsRight;
