/**
 * Outil de Visibilité Optique - VERSION SIMPLE
 * 1. Clic → Œil
 * 2. Glissement → Ligne noire + distance
 * 3. 2ème clic → Cercle vert/rouge + fenêtre résultat
 */

import React, { useRef } from 'react';
import { useMapEvents } from 'react-leaflet';
import L from 'leaflet';

// Style CSS pour FORCER la ligne à rester NOIRE
const forceBlackLineStyle = `
  .visibility-line-always-black {
    stroke: #000000 !important;
    stroke-opacity: 0.9 !important;
    fill: none !important;
  }
`;

// Injecter le style pour forcer la couleur noire
if (typeof document !== 'undefined' && !document.querySelector('#force-black-line-style')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'force-black-line-style';
  styleElement.textContent = forceBlackLineStyle;
  document.head.appendChild(styleElement);
}

// Styles CSS pour FORCER le fond blanc et empêcher toute interférence
const windowStyles = `
  .visibility-result-window {
    background: transparent !important;
    border: none !important;
    z-index: 99999 !important;
  }
  .visibility-result-window > div {
    background: #ffffff !important;
    background-color: #ffffff !important;
    opacity: 1 !important;
    z-index: 99999 !important;
    position: relative !important;
  }
  .visibility-result-popup {
    background: #ffffff !important;
    background-color: #ffffff !important;
    opacity: 1 !important;
    z-index: 99999 !important;
    filter: none !important;
    backdrop-filter: none !important;
  }
  .result-window {
    background: transparent !important;
    border: none !important;
    z-index: 99999 !important;
  }
  .result-window > div {
    background: #ffffff !important;
    background-color: #ffffff !important;
    opacity: 1 !important;
    z-index: 99999 !important;
  }
`;

// Injecter les styles
if (typeof document !== 'undefined' && !document.getElementById('visibility-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'visibility-styles';
  styleSheet.textContent = windowStyles;
  document.head.appendChild(styleSheet);
}

const SimpleVisibilityTool = ({ isActive, map }) => {
  // DEBUG: Log de l'état de l'outil
  console.log('🔧 SimpleVisibilityTool - isActive:', isActive, 'map:', !!map);

  // Références pour les éléments de la carte
  const observerRef = useRef(null);
  const targetRef = useRef(null);
  const eyeMarkerRef = useRef(null);
  const lineRef = useRef(null);
  const distanceRef = useRef(null);
  const circleRef = useRef(null);
  const resultWindowRef = useRef(null);
  const isCompletedRef = useRef(false); // Empêche les clics après le résultat

  // Calcul de distance
  const calculateDistance = (point1, point2) => {
    const R = 6371000; // Rayon de la Terre en mètres
    const lat1Rad = point1.lat * Math.PI / 180;
    const lat2Rad = point2.lat * Math.PI / 180;
    const deltaLat = (point2.lat - point1.lat) * Math.PI / 180;
    const deltaLon = (point2.lng - point1.lng) * Math.PI / 180;

    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Format distance
  const formatDistance = (distanceM) => {
    if (distanceM < 1000) {
      return `${Math.round(distanceM)}m`;
    } else {
      return `${(distanceM / 1000).toFixed(2)}km`;
    }
  };

  // Calcul de visibilité via API avec fallback
  const calculateVisibility = async (observer, target) => {
    try {
      console.log('🔍 Calcul visibilité de', observer, 'vers', target);

      // Essayer d'abord avec l'API
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
      };

      // Ajouter le token seulement s'il existe
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('http://localhost:8000/api/line-of-sight-optimized', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          observer: [observer.lng, observer.lat],
          target: [target.lng, target.lat],
          observer_height: 2.0,
          target_height: 0.0
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Résultat API:', result);
        return result;
      } else {
        console.log('⚠️ API non disponible, utilisation du calcul simple');
        // Fallback : calcul simple basé sur la distance
        return calculateSimpleVisibility(observer, target);
      }
    } catch (error) {
      console.error('❌ Erreur API, utilisation du calcul simple:', error);
      // Fallback : calcul simple basé sur la distance
      return calculateSimpleVisibility(observer, target);
    }
  };

  // Calcul simple de visibilité basé sur la distance
  const calculateSimpleVisibility = (observer, target) => {
    const distance = calculateDistance(observer, target);
    const distanceKm = distance / 1000;

    // Règles simplifiées pour test :
    // - Moins de 5km : 90% visible
    // - 5-15km : 60% visible
    // - 15-30km : 30% visible
    // - Plus de 30km : 10% visible

    let visibilityChance;
    if (distanceKm < 5) {
      visibilityChance = 0.9;
    } else if (distanceKm < 15) {
      visibilityChance = 0.6;
    } else if (distanceKm < 30) {
      visibilityChance = 0.3;
    } else {
      visibilityChance = 0.1;
    }

    const visible = Math.random() < visibilityChance;
    console.log(`🎯 Calcul simple: ${distanceKm.toFixed(1)}km → ${visible ? 'VISIBLE' : 'PAS VISIBLE'} (${(visibilityChance*100).toFixed(0)}% chance)`);

    return {
      visible: visible,
      distance_km: distanceKm,
      calculation_method: 'simple'
    };
  };

  // Mise à jour de la ligne NOIRE avec distance
  const updateLine = (observer, target) => {
    console.log('📏 updateLine appelée - observer:', observer, 'target:', target, 'map:', !!map);
    if (!map || !observer || !target) {
      console.log('❌ updateLine - Paramètres manquants');
      return;
    }

    // Supprimer l'ancienne ligne et distance
    if (lineRef.current) {
      map.removeLayer(lineRef.current);
    }
    if (distanceRef.current) {
      map.removeLayer(distanceRef.current);
    }

    // Ligne TOUJOURS NOIRE (jamais de couleur)
    console.log('📏 Création ligne noire de', observer, 'vers', target);
    lineRef.current = L.polyline(
      [[observer.lat, observer.lng], [target.lat, target.lng]],
      {
        color: '#000000',  // TOUJOURS NOIR - JAMAIS CHANGER
        weight: 3,
        opacity: 0.9,
        className: 'visibility-line-always-black'
      }
    ).addTo(map);
    console.log('✅ Ligne créée et ajoutée à la carte');

    // Distance en blanc sur la ligne
    const distance = calculateDistance(observer, target);
    const distanceText = formatDistance(distance);
    const midLat = (observer.lat + target.lat) / 2;
    const midLng = (observer.lng + target.lng) / 2;

    distanceRef.current = L.marker([midLat, midLng], {
      icon: L.divIcon({
        html: `<div style="color: white; font-size: 14px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">${distanceText}</div>`,
        className: 'distance-label',
        iconSize: [0, 0],
        iconAnchor: [0, 0]
      })
    }).addTo(map);
  };

  // Nettoyage complet
  const clearAll = () => {
    console.log('🧹 Nettoyage complet - Réinitialisation de l\'outil');
    if (map) {
      [eyeMarkerRef, lineRef, distanceRef, circleRef, resultWindowRef].forEach(ref => {
        if (ref.current) {
          map.removeLayer(ref.current);
          ref.current = null;
        }
      });
    }
    observerRef.current = null;
    targetRef.current = null;
    isCompletedRef.current = false; // RÉACTIVER les clics
    console.log('✅ Outil réinitialisé - Prêt pour une nouvelle analyse');
  };

  // Gestion des événements de carte
  useMapEvents({
    click: async (e) => {
      console.log('🖱️ CLIC détecté - isActive:', isActive, 'isCompleted:', isCompletedRef.current);
      if (!isActive) {
        console.log('❌ Outil non actif, clic ignoré');
        return;
      }

      // BLOQUER les clics après le résultat (sauf clic droit pour nettoyer)
      if (isCompletedRef.current) {
        console.log('🚫 Analyse terminée - Clic gauche bloqué. Utilisez clic droit pour recommencer.');
        return;
      }

      const position = { lat: e.latlng.lat, lng: e.latlng.lng };
      console.log('✅ Clic traité à la position:', position);

      if (!observerRef.current) {
        // 1er CLIC : Créer l'œil
        console.log('👁️ Premier clic - Création de l\'œil');

        // Nettoyer les anciens éléments AVANT de définir la nouvelle position
        clearAll();

        // Définir la position d'observation
        observerRef.current = position;

        // Créer l'œil
        eyeMarkerRef.current = L.marker([position.lat, position.lng], {
          icon: L.divIcon({
            html: '<div style="font-size: 20px;">👁️</div>',
            className: 'eye-icon',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
          })
        }).addTo(map);

        console.log('👁️ Œil créé à la position:', position);

      } else {
        // 2ème CLIC : Calculer visibilité
        targetRef.current = position;

        // Calculer visibilité
        const result = await calculateVisibility(observerRef.current, position);
        const isVisible = result?.visible || false;

        // Créer cercle vert/rouge
        const color = isVisible ? '#22c55e' : '#ef4444';
        circleRef.current = L.circleMarker([position.lat, position.lng], {
          radius: 10,
          fillColor: color,
          color: color,
          weight: 3,
          opacity: 1,
          fillOpacity: 0.8
        }).addTo(map);

        // Mettre à jour la ligne finale
        updateLine(observerRef.current, position);

        // Créer fenêtre de résultat
        const distance = calculateDistance(observerRef.current, position);
        const status = isVisible ? "VISIBLE" : "PAS VISIBLE";
        const icon = isVisible ? "✅" : "❌";

        resultWindowRef.current = L.marker([position.lat, position.lng], {
          icon: L.divIcon({
            html: `
              <div class="visibility-result-popup" style="
                background-color: #ffffff !important;
                background: #ffffff !important;
                border: 4px solid ${color} !important;
                border-radius: 15px !important;
                padding: 16px 20px !important;
                font-size: 16px !important;
                font-weight: bold !important;
                color: ${color} !important;
                box-shadow: 0 10px 20px rgba(0,0,0,0.6) !important;
                margin-top: -90px !important;
                text-align: center !important;
                min-width: 160px !important;
                z-index: 99999 !important;
                position: relative !important;
                opacity: 1 !important;
                filter: none !important;
                backdrop-filter: none !important;
              ">
                <div style="margin-bottom: 8px !important; font-size: 18px !important; color: ${color} !important;">${icon} ${status}</div>
                <div style="font-size: 14px !important; color: #000000 !important; font-weight: 700 !important; margin-bottom: 6px !important;">Distance: ${formatDistance(distance)}</div>
                <div style="font-size: 12px !important; color: #333333 !important; margin-top: 8px !important; font-weight: 500 !important;">Clic droit pour recommencer</div>
              </div>
            `,
            className: 'visibility-result-window',
            iconSize: [0, 0],
            iconAnchor: [0, 0]
          })
        }).addTo(map);

        // MARQUER L'ANALYSE COMME TERMINÉE - Bloquer les clics suivants
        isCompletedRef.current = true;
        console.log(`${isVisible ? '🟢 VISIBLE' : '🔴 PAS VISIBLE'} - ${formatDistance(distance)} - ANALYSE TERMINÉE`);
        console.log('🚫 Clics gauche bloqués. Utilisez clic droit pour recommencer.');
      }
    },

    mousemove: (e) => {
      if (!isActive || !observerRef.current || targetRef.current || isCompletedRef.current) {
        // console.log('🖱️ Mouvement ignoré - isActive:', isActive, 'observer:', !!observerRef.current, 'target:', !!targetRef.current, 'completed:', isCompletedRef.current);
        return;
      }

      // Ligne qui suit la souris
      const mousePosition = { lat: e.latlng.lat, lng: e.latlng.lng };
      console.log('🖱️ Mouvement souris - Mise à jour ligne vers:', mousePosition);
      updateLine(observerRef.current, mousePosition);
    },

    contextmenu: (e) => {
      if (!isActive) return;
      e.originalEvent.preventDefault();

      // Clic droit : tout supprimer
      clearAll();
    }
  });

  // Nettoyage lors de la désactivation
  React.useEffect(() => {
    if (!isActive) {
      clearAll();
    }
  }, [isActive]);

  // Nettoyage lors du démontage
  React.useEffect(() => {
    return () => clearAll();
  }, []);

  return null; // Pas d'interface React
};

export default SimpleVisibilityTool;
