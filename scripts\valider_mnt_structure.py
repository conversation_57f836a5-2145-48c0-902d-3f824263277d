#!/usr/bin/env python3
"""
Script de validation de la structure MNT
Vérifie que les fichiers 25MB sont correctement organisés
"""

import os
from pathlib import Path
import json

def valider_structure_mnt():
    """Valider la structure du dossier MNT"""
    
    # Chemin vers le dossier MNT
    mnt_dir = Path("backend/app/MNT")
    
    print("🔍 VALIDATION STRUCTURE MNT")
    print("=" * 40)
    
    if not mnt_dir.exists():
        print(f"❌ Dossier MNT non trouvé: {mnt_dir}")
        print("💡 Créez le dossier et ajoutez vos fichiers TIFF 25MB")
        return False
    
    # Scanner les fichiers TIFF
    fichiers_tiff = list(mnt_dir.rglob("*.tif"))
    
    if not fichiers_tiff:
        print(f"❌ Aucun fichier TIFF trouvé dans {mnt_dir}")
        print("💡 Ajoutez vos fichiers .tif découpés en 25MB")
        return False
    
    print(f"📁 Dossier MNT: {mnt_dir}")
    print(f"📊 Fichiers TIFF trouvés: {len(fichiers_tiff)}")
    print()
    
    # Analyser les fichiers
    stats = {
        'total_files': 0,
        'total_size_mb': 0,
        'size_distribution': {'<10MB': 0, '10-30MB': 0, '30-50MB': 0, '>50MB': 0},
        'folders': {},
        'files_info': []
    }
    
    for tiff_file in fichiers_tiff:
        size_mb = tiff_file.stat().st_size / (1024 * 1024)
        folder = tiff_file.parent.name
        
        stats['total_files'] += 1
        stats['total_size_mb'] += size_mb
        
        # Distribution des tailles
        if size_mb < 10:
            stats['size_distribution']['<10MB'] += 1
        elif size_mb <= 30:
            stats['size_distribution']['10-30MB'] += 1
        elif size_mb <= 50:
            stats['size_distribution']['30-50MB'] += 1
        else:
            stats['size_distribution']['>50MB'] += 1
        
        # Par dossier
        if folder not in stats['folders']:
            stats['folders'][folder] = {'count': 0, 'size_mb': 0}
        stats['folders'][folder]['count'] += 1
        stats['folders'][folder]['size_mb'] += size_mb
        
        # Info détaillée
        stats['files_info'].append({
            'nom': tiff_file.name,
            'dossier': folder,
            'taille_mb': round(size_mb, 1),
            'chemin': str(tiff_file.relative_to(mnt_dir))
        })
    
    # Afficher les statistiques
    print("📊 STATISTIQUES GÉNÉRALES:")
    print(f"   📁 Total fichiers: {stats['total_files']}")
    print(f"   💾 Taille totale: {stats['total_size_mb']:.1f} MB")
    print(f"   📊 Taille moyenne: {stats['total_size_mb']/stats['total_files']:.1f} MB/fichier")
    print()
    
    print("📈 DISTRIBUTION DES TAILLES:")
    for range_size, count in stats['size_distribution'].items():
        percentage = (count / stats['total_files']) * 100
        print(f"   📊 {range_size}: {count} fichiers ({percentage:.1f}%)")
    print()
    
    print("📂 RÉPARTITION PAR DOSSIER:")
    for folder, info in sorted(stats['folders'].items()):
        print(f"   📁 {folder}: {info['count']} fichiers, {info['size_mb']:.1f} MB")
    print()
    
    # Recommandations
    print("💡 RECOMMANDATIONS:")
    
    # Vérifier les tailles optimales
    optimal_files = stats['size_distribution']['10-30MB']
    total_files = stats['total_files']
    optimal_percentage = (optimal_files / total_files) * 100
    
    if optimal_percentage > 80:
        print("   ✅ Excellente distribution des tailles (80%+ entre 10-30MB)")
    elif optimal_percentage > 60:
        print("   🔶 Bonne distribution des tailles (60%+ entre 10-30MB)")
    else:
        print("   ⚠️ Distribution des tailles à optimiser")
        print("      💡 Idéal: fichiers entre 10-30MB pour performance optimale")
    
    # Vérifier l'organisation
    if len(stats['folders']) > 1:
        print("   ✅ Bonne organisation en sous-dossiers")
    else:
        print("   💡 Considérez organiser en sous-dossiers par zone géographique")
    
    # Afficher quelques exemples
    print("\n📋 EXEMPLES DE FICHIERS:")
    for i, file_info in enumerate(stats['files_info'][:5]):
        print(f"   📄 {file_info['nom']} ({file_info['taille_mb']}MB) - {file_info['dossier']}")
    
    if len(stats['files_info']) > 5:
        print(f"   ... et {len(stats['files_info']) - 5} autres fichiers")
    
    # Sauvegarder le rapport
    rapport_file = mnt_dir / "validation_rapport.json"
    with open(rapport_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 Rapport détaillé sauvegardé: {rapport_file}")
    
    # Conclusion
    if stats['total_files'] > 0:
        print(f"\n✅ STRUCTURE MNT VALIDÉE!")
        print(f"🎯 Prêt pour l'indexation:")
        print(f"   python backend/app/indexer_mnt_simple.py")
        return True
    else:
        print(f"\n❌ Structure MNT incomplète")
        return False

def main():
    print("🗺️ VALIDATION MNT POUR C2-EW")
    print("Vérification des fichiers découpés 25MB...")
    print()
    
    if valider_structure_mnt():
        print("\n🚀 PROCHAINES ÉTAPES:")
        print("   1. Si tout est OK: python backend/app/indexer_mnt_simple.py")
        print("   2. Démarrer backend: cd backend && uvicorn app.main:app --reload")
        print("   3. Tester visibilité optique dans C2-EW")
    else:
        print("\n📝 ACTIONS REQUISES:")
        print("   1. Créer le dossier backend/app/MNT/")
        print("   2. Ajouter vos fichiers TIFF découpés (~25MB chacun)")
        print("   3. Organiser en sous-dossiers par zone si possible")
        print("   4. Relancer cette validation")

if __name__ == "__main__":
    main()
