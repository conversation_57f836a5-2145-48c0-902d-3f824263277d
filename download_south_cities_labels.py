#!/usr/bin/env python3
"""
Téléchargement spécialisé des labels pour les villes du sud :
- Mahbes, Farsia, Aqqa
- Toutes les petites villes/positions zone sud
- Frontières Mauritanie et Algérie
- Zoom 6-13 pour visibilité optimale
"""

import os
import requests
import time
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_tile(url, filepath, max_retries=3):
    """Télécharge une tuile avec gestion d'erreurs"""
    if os.path.exists(filepath):
        return False  # <PERSON><PERSON><PERSON><PERSON> téléchargée
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=20, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
            elif response.status_code == 404:
                return False  # Tuile n'existe pas
                
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(1)
    
    return False

def download_city_labels(city_name, lat, lon, zoom_levels, radius_km=50):
    """Télécharge les labels autour d'une ville spécifique"""
    print(f"\n🏙️  LABELS VILLE: {city_name}")
    print(f"📍 Position: {lat}°N, {lon}°E")
    print(f"🔍 Zooms: {zoom_levels}")
    print(f"📏 Rayon: {radius_km} km")
    
    total_downloaded = 0
    total_existing = 0
    
    for zoom in zoom_levels:
        # Calculer la zone autour de la ville
        # 1 degré ≈ 111 km, donc rayon en degrés
        radius_deg = radius_km / 111.0
        
        bounds = {
            'north': lat + radius_deg,
            'south': lat - radius_deg,
            'west': lon - radius_deg,
            'east': lon + radius_deg
        }
        
        # Calcul des tuiles
        x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
        x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
        
        if x_min > x_max:
            x_min, x_max = x_max, x_min
        if y_min > y_max:
            y_min, y_max = y_max, y_min
        
        total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
        print(f"  📊 Zoom {zoom}: {total_tiles} tuiles")
        
        downloaded = 0
        existing = 0
        
        base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
        folder = "tiles/carto_labels"
        
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                url = f"{base_url}/{zoom}/{x}/{y}.png"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
                
                if download_tile(url, filepath):
                    downloaded += 1
                else:
                    existing += 1
                
                time.sleep(0.05)  # Délai court
        
        print(f"  ✅ Zoom {zoom}: {downloaded} nouvelles, {existing} existantes")
        total_downloaded += downloaded
        total_existing += existing
    
    print(f"🎯 {city_name} terminé: {total_downloaded} nouvelles, {total_existing} existantes")
    return total_downloaded, total_existing

def download_zone_labels_multi_zoom(zone_name, bounds, zoom_levels):
    """Télécharge les labels pour une zone sur plusieurs zooms"""
    print(f"\n🗺️  LABELS ZONE: {zone_name}")
    print(f"📍 Limites: {bounds}")
    print(f"🔍 Zooms: {zoom_levels}")
    
    total_downloaded = 0
    total_existing = 0
    
    for zoom in zoom_levels:
        # Calcul des tuiles
        x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
        x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
        
        if x_min > x_max:
            x_min, x_max = x_max, x_min
        if y_min > y_max:
            y_min, y_max = y_max, y_min
        
        total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
        print(f"  📊 Zoom {zoom}: {total_tiles} tuiles")
        
        downloaded = 0
        existing = 0
        
        base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
        folder = "tiles/carto_labels"
        
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                url = f"{base_url}/{zoom}/{x}/{y}.png"
                filepath = f"{folder}/{zoom}/{x}/{y}.png"
                
                if download_tile(url, filepath):
                    downloaded += 1
                    if downloaded % 20 == 0:
                        print(f"    📥 Zoom {zoom}: {downloaded}/{total_tiles}")
                else:
                    existing += 1
                
                time.sleep(0.05)
        
        print(f"  ✅ Zoom {zoom}: {downloaded} nouvelles, {existing} existantes")
        total_downloaded += downloaded
        total_existing += existing
    
    print(f"🎯 {zone_name} terminé: {total_downloaded} nouvelles, {total_existing} existantes")
    return total_downloaded, total_existing

def main():
    print("🏷️  TÉLÉCHARGEMENT LABELS VILLES SUD - SPÉCIALISÉ")
    print("🎯 Objectif: Labels pour toutes villes/positions sud")
    
    # VILLES SPÉCIFIQUES DU SUD
    cities_south = {
        "Mahbes": {"lat": 21.5, "lon": -12.2, "radius": 80},
        "Farsia": {"lat": 22.3, "lon": -13.1, "radius": 60},
        "Aqqa": {"lat": 20.5, "lon": -9.3, "radius": 70},
        "Tichla": {"lat": 21.1, "lon": -11.4, "radius": 50},
        "Bir_Gandouz": {"lat": 21.3, "lon": -11.9, "radius": 40},
        "Tifariti": {"lat": 26.2, "lon": -10.8, "radius": 60},
        "Bir_Lahlou": {"lat": 25.1, "lon": -13.0, "radius": 50},
        "Guelta_Zemmur": {"lat": 25.6, "lon": -12.1, "radius": 40},
        "Aousserd": {"lat": 22.3, "lon": -14.0, "radius": 50},
        "Zouerate": {"lat": 22.7, "lon": -12.5, "radius": 60},
        "Nouadhibou": {"lat": 20.9, "lon": -17.0, "radius": 80},
        "Choum": {"lat": 19.4, "lon": -12.9, "radius": 40},
        "Atar": {"lat": 20.5, "lon": -13.0, "radius": 70}
    }
    
    # ZONES FRONTALIÈRES
    frontier_zones = {
        "Frontiere_Sud_Mauritanie": {
            "bounds": {
                "north": 22.0,
                "south": 19.0,
                "west": -17.0,
                "east": -10.0
            }
        },
        "Frontiere_Est_Algerie": {
            "bounds": {
                "north": 27.0,
                "south": 21.0,
                "west": -2.0,
                "east": 2.0
            }
        },
        "Zone_Tindouf_Etendue": {
            "bounds": {
                "north": 28.0,
                "south": 25.0,
                "west": -10.0,
                "east": -6.0
            }
        }
    }
    
    # Zooms pour visibilité optimale des labels
    city_zoom_levels = [6, 7, 8, 9, 10, 11, 12, 13]
    zone_zoom_levels = [6, 7, 8, 9, 10]
    
    total_downloaded = 0
    total_existing = 0
    
    print(f"\n📊 VILLES À TRAITER: {len(cities_south)}")
    print(f"📊 ZONES À TRAITER: {len(frontier_zones)}")
    
    # Télécharger labels pour chaque ville
    print(f"\n{'='*60}")
    print("🏙️  TÉLÉCHARGEMENT LABELS VILLES")
    
    for city_name, city_data in cities_south.items():
        down, exist = download_city_labels(
            city_name,
            city_data['lat'],
            city_data['lon'],
            city_zoom_levels,
            city_data['radius']
        )
        total_downloaded += down
        total_existing += exist
        time.sleep(1)  # Pause entre villes
    
    # Télécharger labels pour zones frontalières
    print(f"\n{'='*60}")
    print("🗺️  TÉLÉCHARGEMENT LABELS ZONES FRONTALIÈRES")
    
    for zone_name, zone_data in frontier_zones.items():
        down, exist = download_zone_labels_multi_zoom(
            zone_name,
            zone_data['bounds'],
            zone_zoom_levels
        )
        total_downloaded += down
        total_existing += exist
        time.sleep(1)  # Pause entre zones
    
    print(f"\n{'='*60}")
    print("🎉 TÉLÉCHARGEMENT LABELS SUD TERMINÉ!")
    print(f"🔽 Total téléchargé: {total_downloaded}")
    print(f"✅ Total existant: {total_existing}")
    print(f"📁 Dossier: tiles/carto_labels/")
    
    print(f"\n🌍 LABELS DISPONIBLES:")
    print(f"✅ Mahbes, Farsia, Aqqa - labels détaillés")
    print(f"✅ Tichla, Bir Gandouz, Tifariti - labels complets")
    print(f"✅ Toutes petites villes zone sud")
    print(f"✅ Frontières Mauritanie-Algérie")
    print(f"✅ Zoom 6-13 pour visibilité optimale")

if __name__ == "__main__":
    main()
