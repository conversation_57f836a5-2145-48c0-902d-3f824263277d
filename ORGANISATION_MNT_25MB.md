# 📁 Organisation Optimale MNT 25MB pour C2-EW

## 🎯 Structure Recommandée

```
backend/app/MNT/
├── nord/                    # Zones nord du Maroc
│   ├── tanger_*.tif
│   ├── tetouan_*.tif
│   ├── alhoceima_*.tif
│   └── nador_*.tif
├── centre/                  # Zones centre
│   ├── rabat_*.tif
│   ├── casablanca_*.tif
│   ├── fes_*.tif
│   ├── meknes_*.tif
│   └── kenitra_*.tif
├── atlas/                   # Chaînes montagneuses
│   ├── haut_atlas_*.tif
│   ├── moyen_atlas_*.tif
│   └── anti_atlas_*.tif
├── sud/                     # Zones sud
│   ├── marrakech_*.tif
│   ├── agadir_*.tif
│   ├── essaouira_*.tif
│   └── ouarzazate_*.tif
├── sahara/                  # Sahara occidental
│   ├── laayoune_*.tif
│   ├── dakhla_*.tif
│   ├── smara_*.tif
│   └── boujdour_*.tif
└── frontiere/               # Zones frontalières
    ├── oujda_*.tif
    ├── figuig_*.tif
    └── tindouf_*.tif
```

## 🏷️ Nomenclature des Fichiers

### **Format Recommandé :**
```
[zone]_[latitude]_[longitude]_[index].tif

Exemples :
- rabat_34_-7_001.tif
- casa_33_-7_001.tif
- marrakech_31_-8_001.tif
- agadir_30_-9_001.tif
- laayoune_27_-13_001.tif
```

### **Alternative Simple :**
```
[ville/zone]_[numéro].tif

Exemples :
- rabat_001.tif, rabat_002.tif
- casablanca_001.tif, casablanca_002.tif
- marrakech_001.tif, marrakech_002.tif
```

## ✅ Critères de Validation

### **Taille des Fichiers :**
- ✅ **Optimal** : 10-30 MB par fichier
- 🔶 **Acceptable** : 5-50 MB par fichier
- ❌ **À éviter** : <5 MB ou >50 MB

### **Format :**
- ✅ **Format** : GeoTIFF (.tif)
- ✅ **Projection** : WGS84 (EPSG:4326) ou UTM
- ✅ **Compression** : LZW ou sans compression
- ✅ **Type de données** : Float32 ou Int16

### **Couverture :**
- ✅ **Zones prioritaires** : Rabat, Casablanca, frontières
- ✅ **Zones tactiques** : Villes principales, axes routiers
- 🔶 **Zones secondaires** : Zones rurales, montagnes
- 🔶 **Zones étendues** : Sahara, océan

## 🚀 Procédure de Mise en Place

### **1. Préparation**
```bash
# Supprimer l'ancien dossier (si existe)
rm -rf "backend/app/mnt maroc"

# Créer la nouvelle structure
mkdir -p "backend/app/MNT"
mkdir -p "backend/app/MNT/nord"
mkdir -p "backend/app/MNT/centre"
mkdir -p "backend/app/MNT/atlas"
mkdir -p "backend/app/MNT/sud"
mkdir -p "backend/app/MNT/sahara"
mkdir -p "backend/app/MNT/frontiere"
```

### **2. Organisation des Fichiers**
1. **Copier** vos fichiers TIFF 25MB dans les sous-dossiers appropriés
2. **Renommer** selon la nomenclature recommandée (optionnel)
3. **Vérifier** que tous les fichiers sont accessibles

### **3. Validation**
```bash
# Valider la structure
python scripts/valider_mnt_structure.py
```

### **4. Indexation**
```bash
# Créer l'index spatial
python backend/app/indexer_mnt_simple.py
```

### **5. Test**
```bash
# Démarrer le backend
cd backend
uvicorn app.main:app --reload

# Tester dans l'interface C2-EW
# Aller dans "Outils Tactiques" > "Visibilité Optique"
```

## 📊 Optimisations Performance

### **Cache Intelligent**
- **Taille cache** : 10 fichiers simultanés (250MB RAM)
- **Stratégie** : LRU + géographique
- **Préchargement** : Zones actives + voisines

### **Lecture Optimisée**
- **Fenêtrage** : Lecture partielle avec rasterio.windows
- **Parallélisation** : Lecture simultanée de plusieurs fichiers
- **Compression** : Décompression à la volée

### **Index Spatial**
- **R-tree** : Index géographique pour requêtes rapides
- **Métadonnées** : Cache des informations de fichiers
- **Requêtes** : O(log n) au lieu de O(n)

## 🎯 Zones Prioritaires Maroc

### **Priorité 1 - Zones Urbaines/Stratégiques**
- Rabat-Salé (capitale)
- Casablanca (économique)
- Tanger (détroit)
- Agadir (sud-ouest)
- Oujda (frontière est)

### **Priorité 2 - Zones Tactiques**
- Fès-Meknès (centre)
- Marrakech (tourisme)
- Laâyoune (Sahara)
- Nador (nord-est)
- Essaouira (côte)

### **Priorité 3 - Zones Étendues**
- Atlas (montagnes)
- Sahara occidental
- Zones rurales
- Frontières sud

## 🔧 Dépannage

### **Problème : Fichiers non détectés**
```bash
# Vérifier les permissions
ls -la backend/app/MNT/

# Vérifier les extensions
find backend/app/MNT/ -name "*.tif" | head -10
```

### **Problème : Tailles inadéquates**
```bash
# Analyser les tailles
find backend/app/MNT/ -name "*.tif" -exec ls -lh {} \; | sort -k5 -h
```

### **Problème : Projection incorrecte**
```bash
# Vérifier avec gdalinfo
gdalinfo backend/app/MNT/centre/rabat_001.tif
```

## 📈 Métriques de Performance Attendues

### **Avec Fichiers 25MB Optimisés :**
- ⚡ **Chargement fichier** : <50ms
- ⚡ **Calcul visibilité 5km** : <100ms
- ⚡ **Calcul visibilité 20km** : <200ms
- 💾 **Mémoire cache** : <500MB
- 🔍 **Requête spatiale** : <10ms

### **Comparaison avec Gros Fichier :**
- 📈 **10x plus rapide** pour les calculs courts
- 📈 **5x moins de mémoire** utilisée
- 📈 **Pas de découpage** en temps réel
- 📈 **Cache plus efficace**

---

**🎯 Objectif :** Avoir un système de visibilité optique ultra-performant pour C2-EW avec des temps de réponse <200ms garantis !
