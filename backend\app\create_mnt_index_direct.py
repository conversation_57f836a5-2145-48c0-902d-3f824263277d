#!/usr/bin/env python3
"""
Indexeur MNT direct pour les fichiers n[LAT]_w[LON]_1arc_v3.tif
Optimisé pour C2-EW avec gestion robuste des erreurs
"""

import os
import pickle
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Configuration environnement pour éviter conflits
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

# Nettoyer PATH des références PostgreSQL
current_path = os.environ.get('PATH', '')
clean_path = []
for part in current_path.split(os.pathsep):
    if 'postgresql' not in part.lower():
        clean_path.append(part)
os.environ['PATH'] = os.pathsep.join(clean_path)

try:
    from rtree import index
    print("✅ rtree importé avec succès")
except ImportError as e:
    print(f"❌ Erreur import rtree: {e}")
    print("💡 Installez: pip install rtree")
    exit(1)

class IndexeurMNTDirect:
    """Indexeur direct pour fichiers MNT avec nomenclature géographique"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.mnt_directory = self.base_dir / "MNT"
        self.index_file = self.base_dir / "mnt_index.pkl"
        
        self.spatial_index = index.Index()
        self.file_metadata: Dict[int, Dict] = {}
        self.file_counter = 0
        
        print(f"📁 Dossier MNT: {self.mnt_directory}")
        print(f"📋 Fichier index: {self.index_file}")
    
    def extraire_coordonnees_nom(self, nom_fichier: str) -> Optional[Tuple[float, float, float, float]]:
        """Extraire les coordonnées depuis le nom de fichier n[LAT]_w[LON]_1arc_v3.tif"""
        try:
            # Pattern pour n34_w007_1arc_v3.tif
            pattern = r'n(\d+)_w(\d+)_1arc_v3'
            match = re.match(pattern, nom_fichier.lower())
            
            if match:
                lat = int(match.group(1))
                lon = int(match.group(2))
                
                # Convertir en bounds géographiques (1° x 1°)
                # Latitude: n34 = 34°N à 35°N
                # Longitude: w007 = 7°W à 6°W
                min_lat = lat
                max_lat = lat + 1
                min_lon = -lon - 1  # w007 = -8° à -7°
                max_lon = -lon
                
                return (min_lon, min_lat, max_lon, max_lat)
            
            return None
            
        except Exception as e:
            print(f"⚠️ Erreur extraction coordonnées {nom_fichier}: {e}")
            return None
    
    def scanner_fichiers_mnt(self) -> List[Path]:
        """Scanner tous les fichiers TIFF MNT"""
        if not self.mnt_directory.exists():
            print(f"❌ Dossier MNT non trouvé: {self.mnt_directory}")
            return []
        
        # Chercher tous les fichiers .tif
        fichiers_tiff = []
        for tiff_file in self.mnt_directory.glob("*.tif"):
            if tiff_file.is_file() and tiff_file.stat().st_size > 1024:  # > 1KB
                fichiers_tiff.append(tiff_file)
        
        print(f"📊 Fichiers TIFF trouvés: {len(fichiers_tiff)}")
        return sorted(fichiers_tiff)
    
    def creer_metadata_fichier(self, tiff_path: Path) -> Optional[Dict]:
        """Créer les métadonnées d'un fichier basées sur son nom"""
        try:
            # Extraire coordonnées du nom
            bounds = self.extraire_coordonnees_nom(tiff_path.name)
            if not bounds:
                print(f"⚠️ Impossible d'extraire coordonnées: {tiff_path.name}")
                return None
            
            min_lon, min_lat, max_lon, max_lat = bounds
            
            # Métadonnées basiques
            metadata = {
                'path': str(tiff_path),
                'nom': tiff_path.name,
                'bounds': bounds,
                'wgs84_bounds': bounds,  # Déjà en WGS84
                'center_lat': (min_lat + max_lat) / 2,
                'center_lon': (min_lon + max_lon) / 2,
                'taille_mb': tiff_path.stat().st_size / (1024 * 1024),
                'crs': 'EPSG:4326',  # Assumé WGS84
                'resolution_deg': 1.0,  # 1° x 1°
                'type': 'SRTM_1arc'
            }
            
            # Déterminer la zone géographique
            lat_center = metadata['center_lat']
            lon_center = metadata['center_lon']
            
            if lat_center >= 34:
                metadata['zone'] = 'nord'
            elif lat_center >= 31:
                metadata['zone'] = 'centre'
            elif lat_center >= 28:
                metadata['zone'] = 'sud'
            elif lat_center >= 25:
                metadata['zone'] = 'sahara_nord'
            else:
                metadata['zone'] = 'sahara_sud'
            
            # Déterminer les villes/régions proches
            if 33 <= lat_center <= 35 and -8 <= lon_center <= -6:
                metadata['region'] = 'rabat_casa'
            elif 31 <= lat_center <= 33 and -9 <= lon_center <= -7:
                metadata['region'] = 'marrakech'
            elif 30 <= lat_center <= 32 and -10 <= lon_center <= -8:
                metadata['region'] = 'agadir'
            elif 27 <= lat_center <= 29 and -14 <= lon_center <= -12:
                metadata['region'] = 'laayoune'
            elif 23 <= lat_center <= 25 and -17 <= lon_center <= -15:
                metadata['region'] = 'dakhla'
            else:
                metadata['region'] = 'autre'
            
            return metadata
            
        except Exception as e:
            print(f"❌ Erreur création métadonnées {tiff_path.name}: {e}")
            return None
    
    def construire_index(self) -> bool:
        """Construire l'index spatial complet"""
        print("\n🔨 Construction de l'index spatial...")
        
        fichiers_tiff = self.scanner_fichiers_mnt()
        if not fichiers_tiff:
            print("❌ Aucun fichier TIFF trouvé")
            return False
        
        fichiers_indexes = 0
        taille_totale_mb = 0
        zones = {}
        
        for tiff_file in fichiers_tiff:
            metadata = self.creer_metadata_fichier(tiff_file)
            
            if metadata:
                # Ajouter à l'index spatial
                bounds = metadata['wgs84_bounds']
                self.spatial_index.insert(self.file_counter, bounds)
                
                # Stocker métadonnées
                self.file_metadata[self.file_counter] = metadata
                
                fichiers_indexes += 1
                taille_totale_mb += metadata['taille_mb']
                
                # Statistiques par zone
                zone = metadata['zone']
                if zone not in zones:
                    zones[zone] = 0
                zones[zone] += 1
                
                print(f"✅ {tiff_file.name} -> {metadata['zone']} ({metadata['taille_mb']:.1f}MB)")
                self.file_counter += 1
            else:
                print(f"⚠️ Ignoré: {tiff_file.name}")
        
        print(f"\n📊 RÉSUMÉ INDEXATION:")
        print(f"   ✅ Fichiers indexés: {fichiers_indexes}")
        print(f"   💾 Taille totale: {taille_totale_mb:.1f} MB")
        print(f"   📊 Taille moyenne: {taille_totale_mb/fichiers_indexes:.1f} MB/fichier")
        
        print(f"\n🗺️ RÉPARTITION PAR ZONE:")
        for zone, count in sorted(zones.items()):
            print(f"   📍 {zone}: {count} fichiers")
        
        return fichiers_indexes > 0
    
    def sauvegarder_index(self) -> bool:
        """Sauvegarder l'index sur disque"""
        try:
            print("\n💾 Sauvegarde de l'index...")
            
            # Préparer les données
            index_data = {
                'file_metadata': self.file_metadata,
                'file_counter': self.file_counter,
                'mnt_directory': str(self.mnt_directory),
                'stats': {
                    'total_files': len(self.file_metadata),
                    'total_size_mb': sum(m['taille_mb'] for m in self.file_metadata.values()),
                    'zones': list(set(m.get('zone', 'unknown') for m in self.file_metadata.values())),
                    'coverage_bounds': self._calculer_bounds_globaux()
                }
            }
            
            # Sauvegarder métadonnées
            with open(self.index_file, 'wb') as f:
                pickle.dump(index_data, f)
            
            # Sauvegarder index spatial
            spatial_index_file = self.index_file.with_suffix('.idx')
            self.spatial_index.close()
            
            # Recréer et sauvegarder l'index spatial
            self.spatial_index = index.Index(str(spatial_index_file))
            for file_id, metadata in self.file_metadata.items():
                bounds = metadata['wgs84_bounds']
                self.spatial_index.insert(file_id, bounds)
            
            print(f"✅ Index sauvegardé: {self.index_file}")
            print(f"✅ Index spatial: {spatial_index_file}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def _calculer_bounds_globaux(self) -> Tuple[float, float, float, float]:
        """Calculer les bounds globaux de tous les fichiers"""
        if not self.file_metadata:
            return (0, 0, 0, 0)
        
        min_lons = []
        min_lats = []
        max_lons = []
        max_lats = []
        
        for metadata in self.file_metadata.values():
            bounds = metadata['wgs84_bounds']
            min_lons.append(bounds[0])
            min_lats.append(bounds[1])
            max_lons.append(bounds[2])
            max_lats.append(bounds[3])
        
        return (min(min_lons), min(min_lats), max(max_lons), max(max_lats))
    
    def tester_requete(self):
        """Tester une requête spatiale"""
        print(f"\n🧪 Test de requête spatiale...")
        
        # Test avec Rabat (34°N, 7°W)
        test_lon, test_lat = -6.8, 33.9
        print(f"   📍 Point test: Rabat ({test_lon}, {test_lat})")
        
        # Chercher fichiers intersectant
        file_ids = list(self.spatial_index.intersection((test_lon, test_lat, test_lon, test_lat)))
        
        if file_ids:
            print(f"   ✅ Trouvé {len(file_ids)} fichier(s):")
            for file_id in file_ids[:3]:
                metadata = self.file_metadata[file_id]
                print(f"      📄 {metadata['nom']} - {metadata['zone']}")
        else:
            print(f"   ❌ Aucun fichier trouvé pour Rabat")
        
        # Test avec Marrakech (31°N, 8°W)
        test_lon, test_lat = -7.9, 31.6
        print(f"   📍 Point test: Marrakech ({test_lon}, {test_lat})")
        
        file_ids = list(self.spatial_index.intersection((test_lon, test_lat, test_lon, test_lat)))
        if file_ids:
            print(f"   ✅ Trouvé {len(file_ids)} fichier(s) pour Marrakech")
        else:
            print(f"   ❌ Aucun fichier trouvé pour Marrakech")

def main():
    """Fonction principale"""
    print("🗺️ INDEXATION DIRECTE MNT POUR C2-EW")
    print("=" * 50)
    
    # Créer l'indexeur
    indexeur = IndexeurMNTDirect()
    
    # Construire l'index
    if indexeur.construire_index():
        # Sauvegarder
        if indexeur.sauvegarder_index():
            indexeur.tester_requete()
            
            print(f"\n✅ INDEXATION TERMINÉE AVEC SUCCÈS!")
            print(f"🎯 L'outil de visibilité optique est prêt!")
            print(f"\n🚀 PROCHAINES ÉTAPES:")
            print(f"   1. Démarrer backend: cd backend && uvicorn app.main:app --reload")
            print(f"   2. Ouvrir C2-EW dans le navigateur")
            print(f"   3. Aller dans 'Outils Tactiques' > 'Visibilité Optique'")
            print(f"   4. Cliquer sur la carte pour tester!")
        else:
            print(f"❌ Erreur lors de la sauvegarde")
            return False
    else:
        print(f"❌ Erreur lors de la construction de l'index")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
