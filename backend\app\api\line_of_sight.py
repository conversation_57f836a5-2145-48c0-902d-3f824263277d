#!/usr/bin/env python3
"""
Endpoints FastAPI pour Visibilité Optique Professionnelle
Expert SIG - Performance et Précision Garanties
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator
import structlog

from ..services.visibility_service import VisibilityService, Point, VisibilityResult
from ..database import get_db_pool, get_redis_client

logger = structlog.get_logger()

# =====================================================
# MODÈLES PYDANTIC OPTIMISÉS
# =====================================================

class PointModel(BaseModel):
    """Point géographique avec validation stricte"""
    lat: float = Field(..., ge=20.0, le=36.0, description="Latitude (20°N à 36°N pour le Maroc)")
    lon: float = Field(..., ge=-18.0, le=-1.0, description="Longitude (-18°W à -1°W pour le Maroc)")

    @validator('lat', 'lon')
    def validate_precision(cls, v):
        """Validation de la précision (max 6 décimales = ~10cm)"""
        if abs(v) > 180:
            raise ValueError("Coordonnée hors limites")
        return round(v, 6)

class VisibilityRequest(BaseModel):
    """Requête de calcul de visibilité"""
    point1: PointModel = Field(..., description="Point de départ")
    point2: PointModel = Field(..., description="Point d'arrivée")
    observer_height: float = Field(default=2.0, ge=0.0, le=1000.0, description="Hauteur observateur (m)")
    target_height: float = Field(default=0.0, ge=0.0, le=1000.0, description="Hauteur cible (m)")
    use_cache: bool = Field(default=True, description="Utiliser le cache Redis")

    @validator('point1', 'point2')
    def validate_points_different(cls, v, values):
        """Vérifier que les points sont différents"""
        if 'point1' in values:
            p1 = values['point1']
            if abs(v.lat - p1.lat) < 1e-6 and abs(v.lon - p1.lon) < 1e-6:
                raise ValueError("Les points de départ et d'arrivée doivent être différents")
        return v

class VisibilityResponse(BaseModel):
    """Réponse de calcul de visibilité"""
    is_visible: bool = Field(..., description="Visibilité optique possible")
    distance_km: float = Field(..., description="Distance en kilomètres")
    distance_m: float = Field(..., description="Distance en mètres")
    obstruction_distance_m: Optional[float] = Field(None, description="Distance de l'obstruction (m)")
    calculation_time_ms: int = Field(..., description="Temps de calcul (ms)")
    accuracy_class: str = Field(..., description="Classe de précision")
    cache_hit: bool = Field(..., description="Résultat du cache")
    timestamp: datetime = Field(default_factory=datetime.now, description="Horodatage du calcul")

class ElevationRequest(BaseModel):
    """Requête d'élévation"""
    point: PointModel = Field(..., description="Point géographique")

class ElevationResponse(BaseModel):
    """Réponse d'élévation"""
    elevation: float = Field(..., description="Élévation en mètres")
    point: PointModel = Field(..., description="Point géographique")
    interpolated: bool = Field(default=True, description="Élévation interpolée")

class ServiceStatsResponse(BaseModel):
    """Statistiques du service"""
    mnt_points: int = Field(..., description="Nombre de points MNT")
    elevation_range: Dict[str, float] = Field(..., description="Plage d'élévations")
    coverage_bounds: str = Field(..., description="Limites de couverture")
    source_files: int = Field(..., description="Nombre de fichiers source")
    service_status: str = Field(..., description="Statut du service")

# =====================================================
# ROUTER ET DÉPENDANCES
# =====================================================

router = APIRouter(prefix="/api/visibility", tags=["visibility-optical"])

async def get_visibility_service() -> VisibilityService:
    """Dépendance pour obtenir le service de visibilité"""
    db_pool = await get_db_pool()
    redis_client = await get_redis_client()
    return VisibilityService(db_pool, redis_client)

class LineOfSightService:
    """Service de calcul de visibilité optique CONFORME aux spécifications"""
    
    def __init__(self):
        self.app_dir = Path(__file__).parent.parent
        self.mnt_dir = self.app_dir / "MNT"
        self.index_file = self.app_dir / "mnt_spatial_index"
        self.metadata_file = self.app_dir / "mnt_metadata.json"
        
        # Cache des handles rasterio (CRITIQUE pour performance)
        self.raster_cache: Dict[str, Any] = {}
        self.spatial_index = None
        self.tiles_metadata = {}
        
        # Charger l'index R-tree et métadonnées
        self._load_spatial_index()
    
    def _load_spatial_index(self):
        """Charger l'index R-tree et métadonnées"""
        try:
            if not self.index_file.exists() or not self.metadata_file.exists():
                raise FileNotFoundError("Index R-tree non trouvé. Exécutez init_mnt_index_v2.py")
            
            # Charger l'index spatial R-tree
            self.spatial_index = index.Index(str(self.index_file))
            
            # Charger les métadonnées
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.tiles_metadata = {int(k): v for k, v in data['tiles'].items()}
            
            print(f"✅ Index R-tree chargé: {len(self.tiles_metadata)} tuiles")
            
        except Exception as e:
            print(f"❌ Erreur chargement index: {e}")
            self.spatial_index = None
            self.tiles_metadata = {}
    
    def _get_raster_handle(self, file_path: str):
        """Obtenir un handle rasterio avec cache"""
        if file_path not in self.raster_cache:
            try:
                self.raster_cache[file_path] = rasterio.open(file_path)
            except Exception as e:
                print(f"❌ Erreur ouverture {file_path}: {e}")
                return None
        return self.raster_cache[file_path]
    
    def _project_to_native_crs(self, lon: float, lat: float, target_crs: str) -> Tuple[float, float]:
        """Retransformer lon,lat (WGS84) dans le CRS du MNT"""
        if target_crs == 'EPSG:4326':
            return lon, lat
        
        try:
            transformer = pyproj.Transformer.from_crs(
                CRS.from_epsg(4326), CRS.from_string(target_crs), always_xy=True
            )
            return transformer.transform(lon, lat)
        except Exception as e:
            print(f"⚠️ Erreur projection vers {target_crs}: {e}")
            return lon, lat
    
    def _query_intersecting_tiles(self, observer: List[float], target: List[float]) -> List[Dict]:
        """Interroger le R-tree pour lister les GeoTIFFs couvrant la ligne de visée"""
        if not self.spatial_index:
            return []
        
        # Créer une bounding box autour de la ligne de visée
        min_lon = min(observer[0], target[0]) - 0.01  # Buffer de ~1km
        max_lon = max(observer[0], target[0]) + 0.01
        min_lat = min(observer[1], target[1]) - 0.01
        max_lat = max(observer[1], target[1]) + 0.01
        
        # Requête dans l'index R-tree
        tile_ids = list(self.spatial_index.intersection((min_lon, min_lat, max_lon, max_lat)))
        
        # Retourner les métadonnées des tuiles intersectantes
        intersecting_tiles = []
        for tile_id in tile_ids:
            if tile_id in self.tiles_metadata:
                intersecting_tiles.append(self.tiles_metadata[tile_id])
        
        return intersecting_tiles
    
    def _calculate_distance_haversine(self, lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """Calculer la distance horizontale totale L (haversine)"""
        R = 6371000  # Rayon de la Terre en mètres
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def _extract_elevation_profile(self, observer: List[float], target: List[float], 
                                 distance_m: float) -> List[Tuple[float, float, float]]:
        """Extraire le profil d'élévation le long de la ligne de visée"""
        # Choisir N points équidistants (ex. 1 point tous les 10 m, maxi 200)
        if distance_m < 1000:  # < 1km
            sample_interval = 10  # 1 point tous les 10m
        elif distance_m < 5000:  # < 5km
            sample_interval = 25  # 1 point tous les 25m
        else:
            sample_interval = 50  # 1 point tous les 50m
        
        num_points = min(int(distance_m / sample_interval), 200)
        if num_points < 2:
            num_points = 2
        
        profile = []
        
        for i in range(num_points + 1):
            ratio = i / num_points
            
            # Position interpolée
            lon = observer[0] + (target[0] - observer[0]) * ratio
            lat = observer[1] + (target[1] - observer[1]) * ratio
            distance = distance_m * ratio
            
            # Obtenir l'élévation à ce point
            elevation = self._get_elevation_at_point(lon, lat)
            
            profile.append((distance, elevation if elevation is not None else 0.0, ratio))
        
        return profile
    
    def _get_elevation_at_point(self, lon: float, lat: float) -> Optional[float]:
        """Obtenir l'élévation à un point via les tuiles MNT"""
        if not self.spatial_index:
            return None
        
        # Trouver les tuiles contenant ce point
        tile_ids = list(self.spatial_index.intersection((lon, lat, lon, lat)))
        
        for tile_id in tile_ids:
            if tile_id not in self.tiles_metadata:
                continue
            
            metadata = self.tiles_metadata[tile_id]
            
            # Obtenir le handle rasterio
            raster = self._get_raster_handle(metadata['file_path'])
            if not raster:
                continue
            
            try:
                # Projeter vers le CRS natif de la tuile
                native_x, native_y = self._project_to_native_crs(lon, lat, metadata['crs'])
                
                # Convertir en indices de pixel
                row, col = raster.index(native_x, native_y)
                
                # Vérifier les limites
                if 0 <= row < raster.height and 0 <= col < raster.width:
                    # Lire uniquement la fenêtre nécessaire (1x1 pixel)
                    window = Window(col, row, 1, 1)
                    data = raster.read(1, window=window)
                    elevation = data[0, 0]
                    
                    # Gestion des NoData
                    if raster.nodata is not None and elevation == raster.nodata:
                        continue
                    
                    return float(elevation)
                    
            except Exception as e:
                print(f"⚠️ Erreur lecture élévation: {e}")
                continue
        
        return None
    
    def _calculate_visibility(self, profile: List[Tuple[float, float, float]], 
                            observer_height: float, target_height: float) -> Tuple[bool, Optional[float]]:
        """Calcul de visibilité selon spécifications"""
        if len(profile) < 2:
            return True, None
        
        # Élévations de l'observateur et de la cible
        z_obs = profile[0][1] + observer_height
        z_tgt = profile[-1][1] + target_height
        total_distance = profile[-1][0]
        
        # Vérifier chaque point intermédiaire
        for distance, z_terrain, ratio in profile[1:-1]:
            # Hauteur théorique de la ligne de vue à cette distance
            hauteur_theorique = z_obs + (z_tgt - z_obs) * ratio
            
            # Si le terrain est plus haut que la ligne de vue → obstruction
            if z_terrain > hauteur_theorique:
                return False, distance
        
        return True, None
    
    async def calculate_line_of_sight(self, request: LineOfSightRequest) -> LineOfSightResponse:
        """Calcul principal de visibilité optique CONFORME aux spécifications"""
        start_time = time.time()
        
        try:
            observer = request.observer
            target = request.target
            
            # 1. Calculer la distance horizontale totale L (haversine)
            distance_m = self._calculate_distance_haversine(
                observer[0], observer[1], target[0], target[1]
            )
            
            # 2. Recherche des tuiles via R-tree
            intersecting_tiles = self._query_intersecting_tiles(observer, target)
            
            if not intersecting_tiles:
                raise HTTPException(
                    status_code=404, 
                    detail="Aucune tuile MNT trouvée pour cette ligne de visée"
                )
            
            # 3. Extraction du profil d'élévation
            profile = self._extract_elevation_profile(observer, target, distance_m)
            
            # 4. Calcul de visibilité
            visible, obstruction_distance = self._calculate_visibility(
                profile, request.observer_height, request.target_height
            )
            
            calculation_time_ms = (time.time() - start_time) * 1000
            
            # 5. Réponse JSON conforme aux spécifications
            response = LineOfSightResponse(
                visible=visible,
                distance_m=distance_m,
                calculation_time_ms=calculation_time_ms,
                profile_points=len(profile)
            )
            
            if not visible and obstruction_distance:
                response.obstruction_distance_m = obstruction_distance
            
            return response
            
        except Exception as e:
            calculation_time_ms = (time.time() - start_time) * 1000
            raise HTTPException(
                status_code=500,
                detail=f"Erreur calcul visibilité: {str(e)} (temps: {calculation_time_ms:.1f}ms)"
            )

# Instance globale du service
los_service = LineOfSightService()

@router.post("/line-of-sight", response_model=LineOfSightResponse)
async def calculate_line_of_sight(
    request: LineOfSightRequest,
    background_tasks: BackgroundTasks
) -> LineOfSightResponse:
    """
    Route POST /api/line-of-sight
    Reçoit JSON { observer: [lon,lat], target: [lon,lat] }
    Retourne { visible: true|false, distance_m: 1532.4 }
    
    CONFORME AUX SPÉCIFICATIONS C2-EW
    Contrainte: Latence <200 ms
    """
    if not DEPENDENCIES_OK:
        raise HTTPException(
            status_code=503,
            detail="Service indisponible: dépendances manquantes"
        )
    
    return await los_service.calculate_line_of_sight(request)
