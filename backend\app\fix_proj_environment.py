#!/usr/bin/env python3
"""
Script pour résoudre les conflits PROJ entre PostgreSQL et rasterio
"""

import os
import sys
from pathlib import Path

def fix_proj_environment():
    """Configurer l'environnement PROJ pour éviter les conflits"""
    
    print("🔧 Configuration de l'environnement PROJ...")
    
    # Désactiver la base de données PROJ de PostgreSQL
    postgres_proj_paths = [
        r"C:\Program Files\PostgreSQL\16\share\contrib\postgis-3.4\proj",
        r"C:\Program Files\PostgreSQL\15\share\contrib\postgis-3.3\proj",
        r"C:\Program Files\PostgreSQL\14\share\contrib\postgis-3.2\proj",
    ]
    
    # Trouver le chemin PROJ de rasterio/GDAL
    try:
        import rasterio
        from rasterio.env import GDALEnv
        
        # Utiliser l'environnement GDAL de rasterio
        with GDALEnv():
            # Forcer l'utilisation de la base PROJ de rasterio
            os.environ['PROJ_LIB'] = ''  # Laisser GDAL trouver automatiquement
            
            # Désactiver les chemins PostgreSQL
            current_path = os.environ.get('PATH', '')
            new_path_parts = []
            
            for path_part in current_path.split(os.pathsep):
                # Exclure les chemins PostgreSQL qui contiennent PROJ
                if not any(postgres_path.lower() in path_part.lower() for postgres_path in postgres_proj_paths):
                    new_path_parts.append(path_part)
            
            os.environ['PATH'] = os.pathsep.join(new_path_parts)
            
            print("✅ Environnement PROJ configuré")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la configuration PROJ: {e}")
        return False

def test_rasterio():
    """Tester que rasterio fonctionne correctement"""
    try:
        import rasterio
        from rasterio.crs import CRS
        
        # Test simple de création CRS
        crs = CRS.from_epsg(4326)
        print(f"✅ Test CRS réussi: {crs}")
        
        # Test de transformation
        from rasterio.warp import transform
        x, y = transform(CRS.from_epsg(4326), CRS.from_epsg(3857), [-6.8], [33.9])
        print(f"✅ Test transformation réussi: {x[0]:.2f}, {y[0]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test rasterio échoué: {e}")
        return False

if __name__ == "__main__":
    print("🗺️ Résolution des conflits PROJ pour C2-EW MNT")
    print("=" * 50)
    
    if fix_proj_environment():
        if test_rasterio():
            print("\n✅ Configuration PROJ réussie!")
            print("🎯 Vous pouvez maintenant utiliser l'indexation MNT")
        else:
            print("\n❌ Tests rasterio échoués")
            sys.exit(1)
    else:
        print("\n❌ Configuration PROJ échouée")
        sys.exit(1)
