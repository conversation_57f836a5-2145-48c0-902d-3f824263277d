#!/usr/bin/env python3
"""
Script de debug pour vérifier le service MNT
"""

import os
from pathlib import Path

# Configuration environnement
os.environ['PROJ_LIB'] = ''
os.environ['GDAL_DATA'] = ''

try:
    from mnt_service_real import mnt_service_real
    print("✅ Service MNT importé")
except ImportError as e:
    print(f"❌ Erreur import service MNT: {e}")
    exit(1)

def test_mnt_service():
    """Tester le service MNT avec des points connus"""
    
    print("🔍 DIAGNOSTIC SERVICE MNT")
    print("=" * 40)
    
    # Vérifier l'index
    print(f"📊 Fichiers indexés: {len(mnt_service_real.file_index)}")
    
    if mnt_service_real.file_index:
        print("\n📁 Premiers fichiers indexés:")
        for i, ((lat_key, lon_key), file_info) in enumerate(list(mnt_service_real.file_index.items())[:5]):
            bounds = file_info['bounds']
            print(f"   {i+1}. {file_info['path'].name}")
            print(f"      Bounds: {bounds}")
            print(f"      Lat key: {lat_key}, Lon key: {lon_key}")
    
    # Points de test au Maroc
    test_points = [
        ("Rabat", -6.8498, 33.9716),
        ("Casablanca", -7.6164, 33.5731),
        ("Marrakech", -7.9811, 31.6295),
        ("Agadir", -9.5981, 30.4278),
        ("Côte Atlantique", -9.0, 32.0),  # Terrain plat
        ("Atlas Central", -7.5, 31.5),    # Montagne
    ]
    
    print(f"\n🧪 TEST ÉLÉVATIONS:")
    for name, lon, lat in test_points:
        # Trouver le fichier
        file_info = mnt_service_real._find_file_for_point(lon, lat)
        
        if file_info:
            print(f"   📍 {name} ({lon:.4f}, {lat:.4f})")
            print(f"      Fichier: {file_info['path'].name}")
            
            # Obtenir l'élévation
            elevation = mnt_service_real.get_elevation_real(lon, lat)
            if elevation is not None:
                print(f"      Élévation: {elevation:.1f}m")
            else:
                print(f"      ❌ Élévation non trouvée")
        else:
            print(f"   ❌ {name} ({lon:.4f}, {lat:.4f}): Aucun fichier trouvé")
        print()
    
    # Test de calcul de visibilité
    print(f"🎯 TEST CALCUL VISIBILITÉ:")
    
    # Test 1: Terrain plat côtier (devrait être VERT)
    print(f"\n1. Test côte atlantique (terrain plat):")
    observer_lon, observer_lat = -9.0, 32.0
    target_lon, target_lat = -8.9, 32.0  # 10km à l'est
    
    result = mnt_service_real.calculate_line_of_sight_real(
        observer_lon, observer_lat, target_lon, target_lat
    )
    
    print(f"   Observer: ({observer_lon}, {observer_lat})")
    print(f"   Target: ({target_lon}, {target_lat})")
    print(f"   Distance: {result.get('distance_m', 0):.0f}m")
    print(f"   Visible: {result.get('visible', False)}")
    print(f"   Source: {result.get('data_source', 'unknown')}")
    
    # Test 2: Passage montagneux (devrait être ROUGE)
    print(f"\n2. Test Atlas (terrain montagneux):")
    observer_lon, observer_lat = -7.5, 31.4
    target_lon, target_lat = -7.5, 31.6  # 20km au nord à travers l'Atlas
    
    result = mnt_service_real.calculate_line_of_sight_real(
        observer_lon, observer_lat, target_lon, target_lat
    )
    
    print(f"   Observer: ({observer_lon}, {observer_lat})")
    print(f"   Target: ({target_lon}, {target_lat})")
    print(f"   Distance: {result.get('distance_m', 0):.0f}m")
    print(f"   Visible: {result.get('visible', False)}")
    print(f"   Source: {result.get('data_source', 'unknown')}")
    if not result.get('visible', False):
        print(f"   Obstruction à: {result.get('obstruction_distance_m', 0):.0f}m")

def test_file_access():
    """Tester l'accès direct aux fichiers"""
    print(f"\n📁 TEST ACCÈS FICHIERS:")
    
    mnt_dir = Path(__file__).parent / "app" / "MNT"
    if not mnt_dir.exists():
        print(f"❌ Dossier MNT non trouvé: {mnt_dir}")
        return
    
    tiff_files = list(mnt_dir.glob("*.tif"))
    print(f"   Fichiers .tif trouvés: {len(tiff_files)}")
    
    if tiff_files:
        # Tester l'ouverture d'un fichier
        test_file = tiff_files[0]
        print(f"   Test fichier: {test_file.name}")
        
        try:
            import rasterio
            with rasterio.open(test_file) as dataset:
                print(f"   ✅ Ouverture réussie")
                print(f"   CRS: {dataset.crs}")
                print(f"   Bounds: {dataset.bounds}")
                print(f"   Taille: {dataset.width}x{dataset.height}")
                print(f"   NoData: {dataset.nodata}")
                
                # Test lecture d'un pixel
                data = dataset.read(1, window=rasterio.windows.Window(0, 0, 1, 1))
                print(f"   Premier pixel: {data[0, 0]}")
                
        except Exception as e:
            print(f"   ❌ Erreur ouverture: {e}")

if __name__ == "__main__":
    test_file_access()
    test_mnt_service()
