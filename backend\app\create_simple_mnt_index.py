#!/usr/bin/env python3
"""
Indexeur MNT simple sans dépendances complexes
Utilise uniquement la nomenclature des fichiers pour créer l'index
"""

import pickle
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class IndexeurMNTSimple:
    """Indexeur simple basé sur la nomenclature des fichiers"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.mnt_directory = self.base_dir / "MNT"
        self.index_file = self.base_dir / "mnt_index.pkl"
        
        self.file_metadata: Dict[int, Dict] = {}
        self.file_counter = 0
        
        print(f"📁 Dossier MNT: {self.mnt_directory}")
        print(f"📋 Fichier index: {self.index_file}")
    
    def extraire_coordonnees_nom(self, nom_fichier: str) -> Optional[Tuple[float, float, float, float]]:
        """Extraire coordonnées depuis n[LAT]_w[LON]_1arc_v3.tif"""
        try:
            # Pattern pour n34_w007_1arc_v3.tif
            pattern = r'n(\d+)_w(\d+)_1arc_v3'
            match = re.match(pattern, nom_fichier.lower())
            
            if match:
                lat = int(match.group(1))
                lon = int(match.group(2))
                
                # Bounds 1° x 1°
                min_lat = lat
                max_lat = lat + 1
                min_lon = -lon - 1  # w007 = -8° à -7°
                max_lon = -lon
                
                return (min_lon, min_lat, max_lon, max_lat)
            
            return None
            
        except Exception as e:
            print(f"⚠️ Erreur extraction {nom_fichier}: {e}")
            return None
    
    def scanner_fichiers(self) -> List[Path]:
        """Scanner fichiers TIFF"""
        if not self.mnt_directory.exists():
            print(f"❌ Dossier MNT non trouvé: {self.mnt_directory}")
            return []
        
        fichiers = []
        for tiff_file in self.mnt_directory.glob("*.tif"):
            if tiff_file.is_file() and tiff_file.stat().st_size > 1024:
                fichiers.append(tiff_file)
        
        print(f"📊 Fichiers trouvés: {len(fichiers)}")
        return sorted(fichiers)
    
    def creer_metadata(self, tiff_path: Path) -> Optional[Dict]:
        """Créer métadonnées basées sur le nom"""
        bounds = self.extraire_coordonnees_nom(tiff_path.name)
        if not bounds:
            return None
        
        min_lon, min_lat, max_lon, max_lat = bounds
        
        metadata = {
            'path': str(tiff_path),
            'nom': tiff_path.name,
            'bounds': bounds,
            'wgs84_bounds': bounds,
            'center_lat': (min_lat + max_lat) / 2,
            'center_lon': (min_lon + max_lon) / 2,
            'taille_mb': tiff_path.stat().st_size / (1024 * 1024),
            'crs': 'EPSG:4326',
            'resolution_deg': 1.0
        }
        
        # Zone géographique
        lat_center = metadata['center_lat']
        if lat_center >= 34:
            metadata['zone'] = 'nord'
        elif lat_center >= 31:
            metadata['zone'] = 'centre'
        elif lat_center >= 28:
            metadata['zone'] = 'sud'
        else:
            metadata['zone'] = 'sahara'
        
        return metadata
    
    def construire_index(self) -> bool:
        """Construire l'index"""
        print("\n🔨 Construction de l'index...")
        
        fichiers = self.scanner_fichiers()
        if not fichiers:
            return False
        
        zones = {}
        taille_totale = 0
        
        for tiff_file in fichiers:
            metadata = self.creer_metadata(tiff_file)
            
            if metadata:
                self.file_metadata[self.file_counter] = metadata
                
                zone = metadata['zone']
                zones[zone] = zones.get(zone, 0) + 1
                taille_totale += metadata['taille_mb']
                
                print(f"✅ {tiff_file.name} -> {zone} ({metadata['taille_mb']:.1f}MB)")
                self.file_counter += 1
        
        print(f"\n📊 RÉSUMÉ:")
        print(f"   ✅ Fichiers: {len(self.file_metadata)}")
        print(f"   💾 Taille: {taille_totale:.1f} MB")
        
        for zone, count in sorted(zones.items()):
            print(f"   📍 {zone}: {count} fichiers")
        
        return len(self.file_metadata) > 0
    
    def sauvegarder(self) -> bool:
        """Sauvegarder l'index"""
        try:
            print(f"\n💾 Sauvegarde...")
            
            # Calculer bounds globaux
            if self.file_metadata:
                all_bounds = [m['wgs84_bounds'] for m in self.file_metadata.values()]
                min_lons = [b[0] for b in all_bounds]
                min_lats = [b[1] for b in all_bounds]
                max_lons = [b[2] for b in all_bounds]
                max_lats = [b[3] for b in all_bounds]
                
                global_bounds = (min(min_lons), min(min_lats), max(max_lons), max(max_lats))
            else:
                global_bounds = (0, 0, 0, 0)
            
            index_data = {
                'file_metadata': self.file_metadata,
                'file_counter': self.file_counter,
                'mnt_directory': str(self.mnt_directory),
                'stats': {
                    'total_files': len(self.file_metadata),
                    'total_size_mb': sum(m['taille_mb'] for m in self.file_metadata.values()),
                    'coverage_bounds': global_bounds
                }
            }
            
            with open(self.index_file, 'wb') as f:
                pickle.dump(index_data, f)
            
            print(f"✅ Index sauvegardé: {self.index_file}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def tester(self):
        """Test simple"""
        print(f"\n🧪 Test...")
        
        # Test Rabat
        test_lon, test_lat = -6.8, 33.9
        fichiers_rabat = []
        
        for metadata in self.file_metadata.values():
            bounds = metadata['wgs84_bounds']
            if (bounds[0] <= test_lon <= bounds[2] and 
                bounds[1] <= test_lat <= bounds[3]):
                fichiers_rabat.append(metadata['nom'])
        
        if fichiers_rabat:
            print(f"   ✅ Rabat ({test_lon}, {test_lat}): {fichiers_rabat[0]}")
        else:
            print(f"   ❌ Aucun fichier pour Rabat")

def main():
    print("🗺️ INDEXATION MNT SIMPLE")
    print("=" * 40)
    
    indexeur = IndexeurMNTSimple()
    
    if indexeur.construire_index():
        if indexeur.sauvegarder():
            indexeur.tester()
            
            print(f"\n✅ SUCCÈS!")
            print(f"🎯 Index MNT créé pour C2-EW")
            print(f"\n🚀 ÉTAPES SUIVANTES:")
            print(f"   1. cd backend")
            print(f"   2. uvicorn app.main:app --reload")
            print(f"   3. Tester visibilité optique")
            return True
    
    print(f"❌ Échec")
    return False

if __name__ == "__main__":
    main()
