#!/usr/bin/env python3
"""
Téléchargement spécialisé :
1. Labels Al <PERSON> et Al Farsia (zoom 6-13)
2. <PERSON><PERSON> zoom 11, 12, 13 (satellite + labels)
"""

import os
import requests
import time
import math
from pathlib import Path
import concurrent.futures
from threading import Lock

# Verrous pour thread safety
download_lock = Lock()
stats_lock = Lock()

# Statistiques globales
global_stats = {
    'downloaded': 0,
    'existing': 0,
    'errors': 0
}

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def download_single_tile(args):
    """Télécharge une seule tuile (pour threading)"""
    url, filepath, tile_type = args
    
    if os.path.exists(filepath):
        with stats_lock:
            global_stats['existing'] += 1
        return False  # Déjà téléchargée
    
    max_retries = 2
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                with download_lock:
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                
                with stats_lock:
                    global_stats['downloaded'] += 1
                    if global_stats['downloaded'] % 100 == 0:
                        print(f"📥 {tile_type}: {global_stats['downloaded']} téléchargées")
                
                return True
            elif response.status_code == 404:
                return False  # Tuile n'existe pas
                
        except Exception as e:
            if attempt == max_retries - 1:
                with stats_lock:
                    global_stats['errors'] += 1
            time.sleep(0.3)
    
    return False

def download_city_area(city_name, lat, lon, zoom_levels, radius_km, tile_type="labels", max_workers=6):
    """Télécharge les tuiles autour d'une ville spécifique"""
    print(f"\n🏙️  {tile_type.upper()} VILLE: {city_name}")
    print(f"📍 Position: {lat}°N, {lon}°E")
    print(f"🔍 Zooms: {zoom_levels}")
    print(f"📏 Rayon: {radius_km} km")
    
    total_downloaded = 0
    total_existing = 0
    
    for zoom in zoom_levels:
        # Calculer la zone autour de la ville
        radius_deg = radius_km / 111.0  # 1 degré ≈ 111 km
        
        bounds = {
            'north': lat + radius_deg,
            'south': lat - radius_deg,
            'west': lon - radius_deg,
            'east': lon + radius_deg
        }
        
        # Calcul des tuiles
        x_min, y_max = deg2num(bounds['north'], bounds['west'], zoom)
        x_max, y_min = deg2num(bounds['south'], bounds['east'], zoom)
        
        if x_min > x_max:
            x_min, x_max = x_max, x_min
        if y_min > y_max:
            y_min, y_max = y_max, y_min
        
        total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
        print(f"  📊 Zoom {zoom}: {total_tiles} tuiles")
        
        # Configuration selon le type
        if tile_type == "satellite":
            base_url = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile"
            folder = "tiles/esri_satellite_morocco"
        else:  # labels
            base_url = "https://cartodb-basemaps-a.global.ssl.fastly.net/light_only_labels"
            folder = "tiles/carto_labels"
        
        # Préparer les tâches de téléchargement
        download_tasks = []
        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                if tile_type == "satellite":
                    url = f"{base_url}/{zoom}/{y}/{x}"
                    filepath = f"{folder}/{zoom}/{x}/{y}.png"
                else:
                    url = f"{base_url}/{zoom}/{x}/{y}.png"
                    filepath = f"{folder}/{zoom}/{x}/{y}.png"
                
                download_tasks.append((url, filepath, f"{city_name}-{tile_type}-Z{zoom}"))
        
        # Téléchargement parallèle
        start_downloaded = global_stats['downloaded']
        start_existing = global_stats['existing']
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            list(executor.map(download_single_tile, download_tasks))
        
        zoom_downloaded = global_stats['downloaded'] - start_downloaded
        zoom_existing = global_stats['existing'] - start_existing
        
        print(f"  ✅ Zoom {zoom}: {zoom_downloaded} nouvelles, {zoom_existing} existantes")
        total_downloaded += zoom_downloaded
        total_existing += zoom_existing
        
        time.sleep(0.5)  # Pause entre zooms
    
    print(f"🎯 {city_name} {tile_type} terminé: {total_downloaded} nouvelles, {total_existing} existantes")
    return total_downloaded, total_existing

def main():
    print("🗺️  TÉLÉCHARGEMENT SPÉCIALISÉ - MAHBES/FARSIA/TINDOUF")
    print("🎯 Objectif: Labels villes + Tuiles détaillées Tindouf")
    
    # COORDONNÉES PRÉCISES DES VILLES
    cities = {
        "Al_Mahbes": {
            "name": "Al Mahbes",
            "lat": 21.52,
            "lon": -12.18,
            "radius": 60  # km
        },
        "Al_Farsia": {
            "name": "Al Farsia", 
            "lat": 22.31,
            "lon": -13.08,
            "radius": 50  # km
        },
        "Tindouf": {
            "name": "Tindouf",
            "lat": 27.67,
            "lon": -8.15,
            "radius": 40  # km
        }
    }
    
    # ZOOMS POUR LABELS (villes sud)
    label_zoom_levels = [6, 7, 8, 9, 10, 11, 12, 13]
    
    # ZOOMS POUR TINDOUF DÉTAILLÉ
    tindouf_detail_zooms = [11, 12, 13]
    
    print(f"\n📊 PLAN DE TÉLÉCHARGEMENT:")
    print(f"🏷️  Labels Al Mahbes: Zooms {label_zoom_levels}")
    print(f"🏷️  Labels Al Farsia: Zooms {label_zoom_levels}")
    print(f"📡 Satellite Tindouf: Zooms {tindouf_detail_zooms}")
    print(f"🏷️  Labels Tindouf: Zooms {tindouf_detail_zooms}")
    
    total_downloaded = 0
    total_existing = 0
    
    # 1. TÉLÉCHARGER LABELS AL MAHBES
    print(f"\n{'='*70}")
    print("🏷️  PHASE 1: LABELS AL MAHBES")
    
    down, exist = download_city_area(
        cities["Al_Mahbes"]["name"],
        cities["Al_Mahbes"]["lat"],
        cities["Al_Mahbes"]["lon"],
        label_zoom_levels,
        cities["Al_Mahbes"]["radius"],
        "labels",
        max_workers=4
    )
    total_downloaded += down
    total_existing += exist
    
    # 2. TÉLÉCHARGER LABELS AL FARSIA
    print(f"\n{'='*70}")
    print("🏷️  PHASE 2: LABELS AL FARSIA")
    
    down, exist = download_city_area(
        cities["Al_Farsia"]["name"],
        cities["Al_Farsia"]["lat"],
        cities["Al_Farsia"]["lon"],
        label_zoom_levels,
        cities["Al_Farsia"]["radius"],
        "labels",
        max_workers=4
    )
    total_downloaded += down
    total_existing += exist
    
    # 3. TÉLÉCHARGER SATELLITE TINDOUF DÉTAILLÉ
    print(f"\n{'='*70}")
    print("📡 PHASE 3: SATELLITE TINDOUF DÉTAILLÉ")
    
    down, exist = download_city_area(
        cities["Tindouf"]["name"],
        cities["Tindouf"]["lat"],
        cities["Tindouf"]["lon"],
        tindouf_detail_zooms,
        cities["Tindouf"]["radius"],
        "satellite",
        max_workers=4
    )
    total_downloaded += down
    total_existing += exist
    
    # 4. TÉLÉCHARGER LABELS TINDOUF DÉTAILLÉ
    print(f"\n{'='*70}")
    print("🏷️  PHASE 4: LABELS TINDOUF DÉTAILLÉ")
    
    down, exist = download_city_area(
        cities["Tindouf"]["name"],
        cities["Tindouf"]["lat"],
        cities["Tindouf"]["lon"],
        tindouf_detail_zooms,
        cities["Tindouf"]["radius"],
        "labels",
        max_workers=4
    )
    total_downloaded += down
    total_existing += exist
    
    print(f"\n{'='*70}")
    print("🎉 TÉLÉCHARGEMENT SPÉCIALISÉ TERMINÉ!")
    print(f"🔽 Total téléchargé: {global_stats['downloaded']}")
    print(f"✅ Total existant: {global_stats['existing']}")
    print(f"❌ Total erreurs: {global_stats['errors']}")
    print(f"📁 Dossiers: tiles/esri_satellite_morocco/ et tiles/carto_labels/")
    
    print(f"\n🌍 RÉSULTATS OBTENUS:")
    print(f"✅ Al Mahbes - Labels zoom 6-13 complets")
    print(f"✅ Al Farsia - Labels zoom 6-13 complets")
    print(f"✅ Tindouf - Satellite zoom 11-13 détaillé")
    print(f"✅ Tindouf - Labels zoom 11-13 détaillé")
    print(f"✅ Navigation ultra-détaillée disponible")
    
    # STATISTIQUES DÉTAILLÉES
    print(f"\n📊 STATISTIQUES PAR ZOOM:")
    for zoom in [11, 12, 13]:
        sat_folder = f"tiles/esri_satellite_morocco/{zoom}"
        lab_folder = f"tiles/carto_labels/{zoom}"
        
        sat_count = 0
        lab_count = 0
        
        if os.path.exists(sat_folder):
            for root, dirs, files in os.walk(sat_folder):
                sat_count += len([f for f in files if f.endswith('.png')])
        
        if os.path.exists(lab_folder):
            for root, dirs, files in os.walk(lab_folder):
                lab_count += len([f for f in files if f.endswith('.png')])
        
        print(f"🔍 Zoom {zoom}: {sat_count} satellite, {lab_count} labels")

if __name__ == "__main__":
    main()
