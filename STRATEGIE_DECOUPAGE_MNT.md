# 🗺️ Stratégie de Découpage MNT 12.6GB pour C2-EW

## 📊 Analyse du Problème

**Fichier source** : MNT Maroc complet (12.6GB)
**Objectif** : Découpage optimal pour performances C2-EW
**Contrainte** : Latence < 200ms pour calculs de visibilité

## 🎯 Stratégie Recommandée

### **Option 1 : Découpage par Tuiles Géographiques (RECOMMANDÉ)**

```
Taille de tuile : 1° x 1° (environ 111km x 111km)
Taille fichier : ~25-50MB par tuile
Nombre de tuiles : ~200-300 pour couvrir le Maroc étendu
```

**Avantages :**
- Accès spatial optimal avec R-tree
- Cache efficace (10 tuiles = ~500MB RAM)
- Parallélisation possible
- Maintenance facile

### **Option 2 : Découpage par Zones Opérationnelles**

```
Zone Nord : Tanger-Tétouan-Al <PERSON>, Oriental, Fès-Meknès
Zone Centre : Rabat-Salé-Kénitra, Casablanca-Settat, Béni <PERSON>-Khénifra
Zone Sud : Marrakech-Safi, Drâa-Tafilalet, Souss-Massa
Zone Sahara : Laâyoune-Sakia El Hamra, Dakhla-Oued Ed-Dahab
```

## 🔧 Script de Découpage Automatique

### Découpage Intelligent par Tuiles

```python
def decouper_mnt_optimal(fichier_source, dossier_sortie, taille_tuile=1.0):
    """
    Découper le MNT en tuiles optimales pour C2-EW
    
    Args:
        fichier_source: Chemin vers le fichier MNT 12.6GB
        dossier_sortie: Dossier pour les tuiles
        taille_tuile: Taille en degrés (1.0 = 1°x1°)
    """
    
    # Paramètres optimisés pour C2-EW
    TAILLE_TUILE = taille_tuile  # 1° = ~111km
    OVERLAP = 0.01  # 1% de chevauchement pour éviter les artefacts
    COMPRESSION = 'LZW'  # Compression sans perte
    TILED = True  # Format tuilé interne pour accès rapide
    
    # Zones d'intérêt prioritaires
    zones_prioritaires = {
        'maroc_nord': {'bounds': (-6.5, 34.0, -1.0, 36.0), 'priorite': 1},
        'maroc_centre': {'bounds': (-8.0, 31.0, -4.0, 34.0), 'priorite': 1},
        'maroc_sud': {'bounds': (-10.0, 28.0, -6.0, 31.0), 'priorite': 2},
        'sahara_ouest': {'bounds': (-17.0, 20.5, -8.0, 28.0), 'priorite': 3},
        'frontiere_est': {'bounds': (-4.0, 30.0, -1.0, 35.0), 'priorite': 2}
    }
```

### Optimisations Performance

```python
# Configuration COG (Cloud Optimized GeoTIFF)
cog_profile = {
    'driver': 'GTiff',
    'compress': 'LZW',
    'tiled': True,
    'blockxsize': 512,
    'blockysize': 512,
    'BIGTIFF': 'IF_SAFER',
    'NUM_THREADS': 'ALL_CPUS'
}

# Pyramides pour zoom multi-échelle
overviews = [2, 4, 8, 16, 32]  # Facteurs de réduction
```

## 📁 Structure de Fichiers Proposée

```
backend/app/MNT/
├── tiles_1deg/           # Tuiles 1°x1° (priorité haute)
│   ├── n35_w007.tif     # Tanger-Tétouan
│   ├── n34_w006.tif     # Rabat-Casablanca
│   ├── n33_w007.tif     # Marrakech
│   └── ...
├── tiles_detailed/       # Zones détaillées (0.5°x0.5°)
│   ├── casablanca/      # Zone urbaine prioritaire
│   ├── rabat/
│   └── frontiere_est/
├── metadata/
│   ├── index_spatial.pkl
│   ├── coverage_map.json
│   └── performance_stats.json
└── cache/               # Cache temporaire
    └── recent_tiles/
```

## ⚡ Optimisations Spécifiques C2-EW

### 1. **Index Spatial Hiérarchique**

```python
# Index à 3 niveaux
index_global = {
    'niveau_1': 'Tuiles 2°x2° (vue générale)',
    'niveau_2': 'Tuiles 1°x1° (opérationnel)',
    'niveau_3': 'Tuiles 0.5°x0.5° (tactique)'
}
```

### 2. **Cache Intelligent**

```python
cache_strategy = {
    'taille_max': '1GB',  # 10% de RAM typique
    'politique': 'LRU + géographique',
    'preload': 'zones_actives + voisines',
    'persistence': 'SSD local'
}
```

### 3. **Lecture Asynchrone**

```python
# Pool de workers pour lecture parallèle
async def load_elevation_profile(start, end):
    # Identifier tuiles nécessaires
    required_tiles = spatial_index.query_line(start, end)
    
    # Charger en parallèle
    tasks = [load_tile_async(tile) for tile in required_tiles]
    tile_data = await asyncio.gather(*tasks)
    
    # Fusionner et extraire profil
    return merge_and_extract_profile(tile_data, start, end)
```

## 🎯 Paramètres Recommandés

### **Pour Zones Urbaines/Tactiques**
- **Taille** : 0.25° x 0.25° (~28km x 28km)
- **Résolution** : Maximale disponible
- **Priorité** : Cache permanent

### **Pour Zones Rurales/Opérationnelles**
- **Taille** : 1° x 1° (~111km x 111km)
- **Résolution** : Standard
- **Priorité** : Cache dynamique

### **Pour Zones Désertiques**
- **Taille** : 2° x 2° (~222km x 222km)
- **Résolution** : Réduite acceptable
- **Priorité** : Chargement à la demande

## 📈 Métriques de Performance Attendues

```
Temps d'accès tuile :     < 50ms
Calcul visibilité 5km :   < 100ms
Calcul visibilité 20km :  < 200ms
Mémoire cache :           < 1GB
Espace disque total :     ~15-20GB (avec compression)
```

## 🚀 Script de Découpage Prêt à l'Emploi

Je vais créer un script Python qui :

1. **Analyse** votre fichier MNT 12.6GB
2. **Découpe** automatiquement selon la stratégie optimale
3. **Crée** l'index spatial R-tree
4. **Génère** les métadonnées de performance
5. **Valide** l'intégrité des données

**Commande d'exécution :**
```bash
python scripts/decouper_mnt_maroc.py \
    --source "chemin/vers/mnt_maroc_12.6gb.tif" \
    --output "backend/app/MNT/" \
    --strategy "tactical" \
    --compression "lzw" \
    --validate
```

## 💡 Recommandations Finales

1. **Commencer** par les zones prioritaires (Rabat, Casablanca, frontières)
2. **Tester** les performances avec 10-20 tuiles d'abord
3. **Ajuster** la taille des tuiles selon les résultats
4. **Monitorer** l'utilisation mémoire en production
5. **Prévoir** un système de mise à jour incrémentale

Cette stratégie garantit des performances optimales pour votre système C2-EW tout en conservant la précision nécessaire pour les calculs de visibilité optique.
