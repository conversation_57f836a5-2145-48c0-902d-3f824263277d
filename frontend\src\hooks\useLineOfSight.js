/**
 * Hook pour la gestion de la ligne de vue (Line of Sight)
 * Optimisé avec debounce et cache pour les performances
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { API_BASE_URL, API_VERSION } from '@/utils/constants';

const useLineOfSight = () => {
  const [isActive, setIsActive] = useState(false);
  const [observer, setObserver] = useState(null);
  const [target, setTarget] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const { token } = useAuthStore();
  const debounceRef = useRef(null);
  const cacheRef = useRef(new Map());
  const abortControllerRef = useRef(null);

  // Configuration optimisée pour fichiers MNT 25MB
  const DEBOUNCE_DELAY = 50; // ms - Plus rapide avec fichiers optimisés
  const CACHE_SIZE = 100; // Plus de cache avec fichiers légers
  const CACHE_TTL = 60000; // 60 secondes - Cache plus long

  /**
   * Générer une clé de cache
   */
  const getCacheKey = useCallback((obs, tgt, obsHeight = 2.0, tgtHeight = 0.0) => {
    if (!obs || !tgt) return null;
    return `${obs.lng.toFixed(6)},${obs.lat.toFixed(6)}-${tgt.lng.toFixed(6)},${tgt.lat.toFixed(6)}-${obsHeight}-${tgtHeight}`;
  }, []);

  /**
   * Nettoyer le cache expiré
   */
  const cleanCache = useCallback(() => {
    const now = Date.now();
    const cache = cacheRef.current;
    
    for (const [key, value] of cache.entries()) {
      if (now - value.timestamp > CACHE_TTL) {
        cache.delete(key);
      }
    }
    
    // Limiter la taille du cache
    if (cache.size > CACHE_SIZE) {
      const entries = Array.from(cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Supprimer les plus anciens
      const toDelete = entries.slice(0, cache.size - CACHE_SIZE);
      toDelete.forEach(([key]) => cache.delete(key));
    }
  }, []);

  /**
   * Calculer la distance entre deux points (approximation rapide)
   */
  const calculateDistance = useCallback((point1, point2) => {
    if (!point1 || !point2) return 0;
    
    const R = 6371000; // Rayon de la Terre en mètres
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    const lat1 = point1.lat * Math.PI / 180;
    const lat2 = point2.lat * Math.PI / 180;

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }, []);

  /**
   * Appel API pour calculer la ligne de vue
   */
  const calculateLineOfSight = useCallback(async (observerPoint, targetPoint, observerHeight = 2.0, targetHeight = 0.0) => {
    if (!observerPoint || !targetPoint || !token) {
      return null;
    }

    // Vérifier le cache
    const cacheKey = getCacheKey(observerPoint, targetPoint, observerHeight, targetHeight);
    const cached = cacheRef.current.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.data;
    }

    try {
      // Annuler la requête précédente
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      abortControllerRef.current = new AbortController();

      // Utiliser l'endpoint optimisé avec MNT réel
      let response = await fetch(`${API_BASE_URL}/line-of-sight-optimized`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          observer: [observerPoint.lng, observerPoint.lat],  // Format conforme [lon, lat]
          target: [targetPoint.lng, targetPoint.lat],        // Format conforme [lon, lat]
          observer_height: observerHeight,
          target_height: targetHeight
        }),
        signal: abortControllerRef.current.signal
      });

      // Fallback vers l'ancien endpoint si le nouveau n'existe pas
      if (!response.ok && response.status === 404) {
        response = await fetch(`${API_BASE_URL}${API_VERSION}/los/line-of-sight`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            observer: {
              lon: observerPoint.lng,
              lat: observerPoint.lat
            },
            target: {
              lon: targetPoint.lng,
              lat: targetPoint.lat
            },
            observer_height: observerHeight,
            target_height: targetHeight
          }),
          signal: abortControllerRef.current.signal
        });
      }

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }

      const data = await response.json();
      
      // Mettre en cache
      cacheRef.current.set(cacheKey, {
        data,
        timestamp: Date.now()
      });
      
      // Nettoyer le cache
      cleanCache();
      
      return data;
      
    } catch (error) {
      if (error.name === 'AbortError') {
        return null; // Requête annulée
      }
      throw error;
    }
  }, [token, getCacheKey, cleanCache]);

  /**
   * Calculer la ligne de vue avec debounce
   */
  const debouncedCalculate = useCallback((observerPoint, targetPoint) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(async () => {
      if (!observerPoint || !targetPoint) {
        setResult(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const losResult = await calculateLineOfSight(observerPoint, targetPoint);
        
        if (losResult) {
          // Ajouter la distance calculée côté client pour validation
          const clientDistance = calculateDistance(observerPoint, targetPoint);
          
          setResult({
            ...losResult,
            client_distance_m: clientDistance,
            observer_point: observerPoint,
            target_point: targetPoint
          });
        }
      } catch (err) {
        console.error('Erreur lors du calcul de ligne de vue:', err);
        setError(err.message || 'Erreur lors du calcul');
        setResult(null);
      } finally {
        setLoading(false);
      }
    }, DEBOUNCE_DELAY);
  }, [calculateLineOfSight, calculateDistance]);

  /**
   * Activer l'outil de ligne de vue
   */
  const activateTool = useCallback(() => {
    setIsActive(true);
    setObserver(null);
    setTarget(null);
    setResult(null);
    setError(null);
  }, []);

  /**
   * Désactiver l'outil
   */
  const deactivateTool = useCallback(() => {
    setIsActive(false);
    setObserver(null);
    setTarget(null);
    setResult(null);
    setError(null);
    setLoading(false);
    
    // Annuler les requêtes en cours
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Nettoyer le debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
  }, []);

  /**
   * Définir la position de l'observateur
   */
  const setObserverPosition = useCallback((position) => {
    setObserver(position);
    
    // Si on a déjà une cible, recalculer
    if (position && target) {
      debouncedCalculate(position, target);
    }
  }, [target, debouncedCalculate]);

  /**
   * Définir la position de la cible (avec debounce)
   */
  const setTargetPosition = useCallback((position) => {
    setTarget(position);
    
    // Si on a un observateur, calculer la ligne de vue
    if (observer && position) {
      debouncedCalculate(observer, position);
    }
  }, [observer, debouncedCalculate]);

  /**
   * Réinitialiser les positions
   */
  const reset = useCallback(() => {
    setObserver(null);
    setTarget(null);
    setResult(null);
    setError(null);
    setLoading(false);
  }, []);

  /**
   * Obtenir la couleur de la ligne selon la visibilité
   */
  const getLineColor = useCallback(() => {
    if (!result) return '#888888'; // Gris par défaut
    return result.visible ? '#4CAF50' : '#F44336'; // Vert si visible, rouge sinon
  }, [result]);

  /**
   * Obtenir le texte de distance formaté
   */
  const getDistanceText = useCallback(() => {
    if (!result) return '';
    
    const distance = result.distance_m;
    if (distance < 1000) {
      return `${Math.round(distance)} m`;
    } else {
      return `${(distance / 1000).toFixed(1)} km`;
    }
  }, [result]);

  /**
   * Obtenir le statut de visibilité
   */
  const getVisibilityStatus = useCallback(() => {
    if (loading) return 'Calcul en cours...';
    if (error) return `Erreur: ${error}`;
    if (!result) return '';
    
    if (result.visible) {
      return `✅ Vue libre - ${getDistanceText()}`;
    } else {
      const reason = result.reason || 'Terrain obstrué';
      return `❌ ${reason} - ${getDistanceText()}`;
    }
  }, [loading, error, result, getDistanceText]);

  // Nettoyage lors du démontage
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return {
    // État
    isActive,
    observer,
    target,
    result,
    loading,
    error,
    
    // Actions
    activateTool,
    deactivateTool,
    setObserverPosition,
    setTargetPosition,
    reset,
    
    // Utilitaires
    getLineColor,
    getDistanceText,
    getVisibilityStatus,
    calculateDistance
  };
};

export default useLineOfSight;
