#!/usr/bin/env python3
"""
Script pour télécharger les tuiles de labels (noms de villes) offline
FOCALISÉ sur le Maroc + marges importantes (Algérie, Mauritanie)
"""

import os
import requests
import time
import math
from pathlib import Path

def lat_lon_to_tile(lat, lon, z):
    """Convertir latitude/longitude en coordonnées de tuile"""
    lat_rad = math.radians(lat)
    n = 2.0 ** z
    x = int((lon + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def download_tile(x, y, z, base_url, output_dir, session):
    """Télécharger une tuile de labels"""
    tile_url = base_url.format(z=z, x=x, y=y, s='a')
    tile_path = output_dir / str(z) / str(x) / f"{y}.png"
    
    # Créer les dossiers si nécessaire
    tile_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Vérifier si la tuile existe déjà
    if tile_path.exists():
        return True
    
    try:
        response = session.get(tile_url, timeout=15)
        if response.status_code == 200:
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            print(f"🔽 Labels Z{z}: {x}/{y}")
            return True
        else:
            return False
    except Exception as e:
        return False

def get_morocco_bounds_for_zoom(zoom):
    """Définir les limites géographiques du Maroc + marges selon le zoom"""
    
    # LIMITES GÉOGRAPHIQUES FOCALISÉES SUR LE MAROC
    if zoom <= 7:
        # Zoom 6-7 : Vue étendue Maroc + Algérie + Mauritanie
        bounds = {
            'north': 36.5,   # Nord Tanger + marge
            'south': 19.0,   # Sud Mauritanie (Nouadhibou)
            'west': -18.0,   # Océan + marge ouest
            'east': 3.0      # Algérie (Oran, Alger) + marge
        }
    elif zoom <= 9:
        # Zoom 8-9 : Vue Maroc détaillée + marges importantes
        bounds = {
            'north': 36.2,   # Nord Tanger
            'south': 20.0,   # Sud Mauritanie
            'west': -17.5,   # Océan + Dakhla
            'east': 2.0      # Algérie (Alger, Oran)
        }
    elif zoom <= 11:
        # Zoom 10-11 : Vue Maroc + zones sud importantes
        bounds = {
            'north': 36.0,   # Nord Maroc
            'south': 21.0,   # Mauritanie (Nouadhibou)
            'west': -17.0,   # Dakhla + océan
            'east': 1.0      # Algérie (frontière)
        }
    else:
        # Zoom 12-13 : Vue Maroc centrée + villes importantes
        bounds = {
            'north': 35.8,   # Nord Maroc
            'south': 22.0,   # Zone sud (Dakhla)
            'west': -16.0,   # Dakhla
            'east': 0.0      # Frontière Algérie
        }
    
    return bounds

def main():
    # Configuration pour labels
    BASE_URL = "https://{s}.basemaps.cartocdn.com/light_only_labels/{z}/{x}/{y}.png"
    OUTPUT_DIR = Path("tiles/carto_labels")
    
    print(f"🏷️ TÉLÉCHARGEMENT LABELS MAROC FOCALISÉ")
    print(f"🎯 Zones: Maroc + Algérie + Mauritanie + Sud")
    
    # Créer session HTTP
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    total_downloaded = 0
    total_existing = 0
    
    # Télécharger pour zooms 6 à 13
    for zoom in range(6, 14):
        print(f"\n📊 ZOOM {zoom}")
        
        # Obtenir les limites géographiques
        bounds = get_morocco_bounds_for_zoom(zoom)
        
        # Convertir en coordonnées de tuiles (corriger l'ordre)
        x_min, y_min = lat_lon_to_tile(bounds['south'], bounds['west'], zoom)
        x_max, y_max = lat_lon_to_tile(bounds['north'], bounds['east'], zoom)
        
        print(f"📍 Limites géographiques:")
        print(f"   Nord: {bounds['north']}°, Sud: {bounds['south']}°")
        print(f"   Ouest: {bounds['west']}°, Est: {bounds['east']}°")
        print(f"📈 Tuiles: X={x_min}-{x_max}, Y={y_min}-{y_max}")
        
        total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
        print(f"📊 Total tuiles à traiter: {total_tiles}")
        
        downloaded = 0
        existing = 0
        
        # S'assurer que les coordonnées sont dans le bon ordre
        if x_min > x_max:
            x_min, x_max = x_max, x_min
        if y_min > y_max:
            y_min, y_max = y_max, y_min

        for x in range(x_min, x_max + 1):
            for y in range(y_min, y_max + 1):
                tile_path = OUTPUT_DIR / str(zoom) / str(x) / f"{y}.png"
                
                if tile_path.exists():
                    existing += 1
                    continue
                
                success = download_tile(x, y, zoom, BASE_URL, OUTPUT_DIR, session)
                if success:
                    downloaded += 1
                
                # Pause pour éviter la surcharge
                time.sleep(0.05)
                
                # Afficher le progrès
                if (downloaded + existing) % 100 == 0:
                    progress = ((downloaded + existing) / total_tiles) * 100
                    print(f"📊 Z{zoom}: {progress:.1f}% ({downloaded} nouveaux, {existing} existants)")
        
        print(f"✅ Zoom {zoom} terminé: {downloaded} nouveaux, {existing} existants")
        total_downloaded += downloaded
        total_existing += existing
    
    print(f"\n🎉 LABELS MAROC FOCALISÉ TERMINÉ!")
    print(f"🔽 Total téléchargé: {total_downloaded}")
    print(f"✅ Total existant: {total_existing}")
    print(f"📁 Dossier: tiles/carto_labels/")
    
    print(f"\n🏙️ VILLES MAINTENANT COUVERTES:")
    print(f"✅ Maroc Nord: Rabat, Casablanca, Tanger, Fès")
    print(f"✅ Maroc Centre: Marrakech, Meknès")
    print(f"✅ Maroc Sud: Laayoune, Dakhla, Smara")
    print(f"✅ Zone Sud: Tichla, Birhlou, Tifaritit, Tindouf")
    print(f"✅ Mauritanie: Nouadhibou")
    print(f"✅ Algérie: Oran, Alger (frontière)")

if __name__ == "__main__":
    main()
